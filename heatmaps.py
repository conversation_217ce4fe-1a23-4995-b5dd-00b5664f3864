import os
import argparse
import torch
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import json
import pickle
import pandas as pd  # 新增导入pandas库

from models.samba_model import SAMBAModel
import config

plt.rcParams['font.sans-serif'] = ['SimHei']  # 黑体
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题


def parse_args():
    parser = argparse.ArgumentParser(description='绘制SAMBA模型因子权重热力图')
    parser.add_argument('--run_id', type=str, required=True, help='训练运行的ID')
    parser.add_argument('--weight_type', type=str, default='best', choices=['best', 'best_ric'], 
                        help='使用的权重类型: best(基于验证损失的最佳模型) 或 best_ric(基于RIC的最佳模型)')
    parser.add_argument('--output_dir', type=str, default='factor_heatmaps', help='输出目录')
    return parser.parse_args()

def load_model_and_config(run_id, weight_type='best'):
    run_dir = os.path.join(config.OUTPUT_DIR, run_id)
    if not os.path.exists(run_dir):
        raise ValueError(f"找不到指定的运行ID目录: {run_dir}")
    
    with open(os.path.join(run_dir, 'config.json'), 'r') as f:
        run_config = json.load(f)
    
    with open(os.path.join(run_dir, 'data_info.pkl'), 'rb') as f:
        data_info = pickle.load(f)
    
    model = SAMBAModel(
        input_dim=len(run_config['factors']),
        d_model=run_config['d_model'],
        n_layer=run_config['n_layer'],
        num_heads=run_config['num_heads'],
        gnn_k=run_config['gnn_k'],
        node_embedding_dim=run_config['node_embedding_dim'],
        dropout=run_config['dropout']
    ).to(config.DEVICE)
    
    model_filename = 'best_model.pt' if weight_type == 'best' else 'best_ric_model.pt'
    best_model_path = os.path.join(run_dir, model_filename)
    
    if not os.path.exists(best_model_path):
        raise ValueError(f"找不到{weight_type}模型权重: {best_model_path}")
    
    print(f"使用{weight_type}模型权重: {best_model_path}")
    checkpoint = torch.load(best_model_path, map_location=config.DEVICE)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model, run_config, data_info

def plot_factor_heatmaps(model, factor_names, output_dir):
    plt.figure(figsize=(20, 15))
    
    # 1. 特征注意力权重
    attention_weights = model.feature_attention.attention[2].weight.data.cpu().numpy()
    print(f"Attention weights shape: {attention_weights.shape}")
    plt.subplot(2, 2, 1)
    sns.heatmap(attention_weights, cmap='YlOrRd', xticklabels=factor_names, yticklabels=False)
    plt.title('特征注意力权重')
    plt.xlabel('因子')
    plt.ylabel('权重')

    # 2. 节点嵌入
    node_embeddings = model.agc_block.node_embeddings.data.cpu().numpy()
    print(f"Node embeddings shape: {node_embeddings.shape}")
    plt.subplot(2, 2, 2)
    sns.heatmap(node_embeddings, cmap='YlOrRd', xticklabels=False, yticklabels=factor_names)
    plt.title('节点嵌入')
    plt.xlabel('嵌入维度')
    plt.ylabel('因子')

    # 3. 因子投影权重
    factor_proj_weights = model.factor_interaction.factor_proj.weight.data.cpu().numpy()
    print(f"Factor projection weights shape: {factor_proj_weights.shape}")
    plt.subplot(2, 2, 3)
    sns.heatmap(factor_proj_weights, cmap='YlOrRd', xticklabels=False, yticklabels=factor_names)
    plt.title('因子投影权重')
    plt.xlabel('投影维度')
    plt.ylabel('因子')

    # 4. 综合权重
    # 确保所有权重都是一维的
    attention_sum = np.abs(attention_weights).sum(axis=1)
    node_embeddings_sum = np.abs(node_embeddings).mean(axis=1)
    factor_proj_sum = np.abs(factor_proj_weights).mean(axis=1)
    
    print(f"Attention sum shape: {attention_sum.shape}")
    print(f"Node embeddings sum shape: {node_embeddings_sum.shape}")
    print(f"Factor projection sum shape: {factor_proj_sum.shape}")

    # 确保所有权重向量长度相同
    min_length = min(len(attention_sum), len(node_embeddings_sum), len(factor_proj_sum), len(factor_names))
    combined_weights = attention_sum[:min_length] + node_embeddings_sum[:min_length] + factor_proj_sum[:min_length]
    combined_weights = combined_weights / combined_weights.sum()

    plt.subplot(2, 2, 4)
    sns.barplot(x=combined_weights, y=factor_names[:min_length])
    plt.title('综合因子重要性')
    plt.xlabel('归一化重要性')
    plt.ylabel('因子')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'factor_importance_heatmap.png'), dpi=300, bbox_inches='tight')
    print(f"热力图已保存至 {os.path.join(output_dir, 'factor_importance_heatmap.png')}")
    
    # 新增：保存综合因子重要性到CSV文件
    save_factor_importance_to_csv(factor_names[:min_length], combined_weights, 
                                 attention_sum[:min_length], node_embeddings_sum[:min_length], 
                                 factor_proj_sum[:min_length], output_dir)

def save_factor_importance_to_csv(factor_names, combined_weights, attention_weights, 
                                node_embeddings, factor_proj_weights, output_dir):
    """
    保存综合因子重要性到CSV文件
    
    参数:
        factor_names: 因子名称列表
        combined_weights: 综合重要性权重
        attention_weights: 注意力权重
        node_embeddings: 节点嵌入权重
        factor_proj_weights: 因子投影权重
        output_dir: 输出目录
    """
    # 创建DataFrame
    df = pd.DataFrame({
        '因子名称': factor_names,
        '综合重要性': combined_weights,
        '注意力权重': attention_weights,
        '节点嵌入权重': node_embeddings,
        '因子投影权重': factor_proj_weights
    })
    
    # 按综合重要性降序排序
    df = df.sort_values('综合重要性', ascending=False)
    
    # 保存到CSV文件
    csv_path = os.path.join(output_dir, 'factor_importance.csv')
    df.to_csv(csv_path, index=False, encoding='utf_8_sig')  # 使用utf_8_sig编码支持中文
    print(f"综合因子重要性已保存至 {csv_path}")

def main():
    args = parse_args()
    os.makedirs(args.output_dir, exist_ok=True)

    model, run_config, data_info = load_model_and_config(args.run_id, args.weight_type)
    factor_names = run_config['factors']

    plot_factor_heatmaps(model, factor_names, args.output_dir)

if __name__ == "__main__":
    main()