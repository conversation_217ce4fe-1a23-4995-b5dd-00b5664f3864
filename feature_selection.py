import pandas as pd
import lightgbm as lgb
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import os
import sys

# 确保可以导入项目中的config模块
# 假设此脚本与 config.py 在同一项目的根目录
import config

# --- 配置区 ---
SELECTION_CONFIG = {
    # 你希望最终保留的顶级特征数量
    "TOP_N_FEATURES": 100,
    # 可视化图中显示的特征数量
    "PLOT_TOP_N": 100,
    # 输出的特征重要性图表文件名
    "PLOT_FILENAME": "feature_importances.png",
    # 输出的特征重要性CSV文件名
    "CSV_FILENAME": "feature_importances.csv"
}

def load_and_prepare_data():
    """
    加载并准备数据用于特征选择。
    这里我们将数据视为一个整体，暂时忽略时序性，以评估每个特征的全局重要性。
    """
    print(f"正在从 {config.DATA_PATH} 加载数据...")
    df = pd.read_csv(config.DATA_PATH)

    # 使用与data_loader中一致的缺失值处理方式
    print("正在处理缺失值...")
    df = df.sort_values(['ts_code', 'trade_date'])
    df = df.ffill().dropna() # 先前向填充，然后删除仍存在的缺失（通常是开头部分）

    if df.empty:
        raise ValueError("数据在预处理后为空，请检查原始数据和处理逻辑。")

    # 确保所有需要的列都存在
    required_cols = config.DEFAULT_FACTORS + [config.TARGET]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"以下列在数据中缺失: {missing_cols}")

    X = df[config.DEFAULT_FACTORS]
    y = df[config.TARGET]

    print(f"数据准备完毕，共 {len(df)} 条样本，{len(config.DEFAULT_FACTORS)} 个特征。")
    return X, y

def perform_feature_selection(X: pd.DataFrame, y: pd.Series):
    """
    使用LightGBM模型进行训练并提取特征重要性。
    """
    print("开始训练LightGBM模型以评估特征重要性...")
    
    # 定义LGBM回归器模型，使用默认的 'gain' 作为重要性类型
    # 'gain' 指的是使用该特征进行分裂所带来的总增益
    lgbm = lgb.LGBMRegressor(
        objective='regression_l1', # MAE损失，对异常值更鲁棒
        metric='rmse',
        n_estimators=1000,         # 增加树的数量以获得更稳定的重要性
        learning_rate=0.05,
        num_leaves=31,
        max_depth=-1,
        n_jobs=-1,                 # 使用所有CPU核心
        seed=42,
        colsample_bytree=0.8,      # 特征采样
        subsample=0.8,             # 数据采样
        reg_alpha=0.1,             # L1正则化
        reg_lambda=0.1,            # L2正则化
    )

    # 训练模型
    lgbm.fit(X, y, 
             eval_set=[(X, y)], 
             eval_metric='rmse',
             callbacks=[lgb.early_stopping(100, verbose=False)]) # 早停以防过拟合

    print("模型训练完成。")

    # 提取特征重要性
    importances = pd.DataFrame({
        'feature': X.columns,
        'importance': lgbm.feature_importances_,
    }).sort_values('importance', ascending=False)

    return importances

def plot_importances(importances: pd.DataFrame):
    """
    将特征重要性可视化并保存为图片。
    """
    top_n = SELECTION_CONFIG['PLOT_TOP_N']
    plot_data = importances.head(top_n)

    plt.figure(figsize=(12, top_n * 0.3))
    sns.barplot(x='importance', y='feature', data=plot_data, palette='viridis')
    plt.title(f'Top {top_n} Feature Importances from LightGBM', fontsize=16)
    plt.xlabel('Importance (Gain)', fontsize=12)
    plt.ylabel('Feature', fontsize=12)
    plt.grid(axis='x', linestyle='--', alpha=0.6)
    plt.tight_layout()
    
    save_path = os.path.join(config.OUTPUT_DIR, SELECTION_CONFIG['PLOT_FILENAME'])
    plt.savefig(save_path)
    print(f"特征重要性图已保存至: {save_path}")
    plt.close()

def generate_new_config_list(importances: pd.DataFrame):
    """
    根据重要性排名，生成可以直接复制到config.py的新因子列表。
    """
    top_n = SELECTION_CONFIG['TOP_N_FEATURES']
    selected_features = importances.head(top_n)['feature'].tolist()

    print("\n" + "="*80)
    print(f"已选出 Top {top_n} 的特征。请将以下列表复制到你的 `config.py` 文件中，替换原有的 `DEFAULT_FACTORS` 列表：")
    print("="*80 + "\n")

    # 格式化输出，使其易于复制
    output_string = "DEFAULT_FACTORS = [\n"
    for i in range(0, len(selected_features), 4): # 每行4个，方便阅读
        line_features = [f"'{f}'" for f in selected_features[i:i+4]]
        output_string += "    " + ", ".join(line_features) + ",\n"
    output_string += "]"
    
    print(output_string)
    print("\n" + "="*80)


if __name__ == "__main__":
    try:
        # 1. 加载数据
        X, y = load_and_prepare_data()

        # 2. 执行特征选择
        importances_df = perform_feature_selection(X, y)
        
        # 3. 保存重要性到CSV文件
        csv_save_path = os.path.join(config.OUTPUT_DIR, SELECTION_CONFIG['CSV_FILENAME'])
        importances_df.to_csv(csv_save_path, index=False)
        print(f"完整的特征重要性已保存至: {csv_save_path}")

        # 4. 可视化
        plot_importances(importances_df)

        # 5. 生成新配置
        generate_new_config_list(importances_df)

    except Exception as e:
        print(f"\n发生错误: {e}")
        import traceback
        traceback.print_exc()

