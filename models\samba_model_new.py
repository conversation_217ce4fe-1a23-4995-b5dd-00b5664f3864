import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
import sys
import os

# 导入配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config


class SwiGLU(nn.Module):
    """
    SwiGLU 激活函数，用于替代标准FFN中的GELU部分。
    来自 "GLU Variants Improve Transformer" 论文。
    """
    def __init__(self, in_features, hidden_features=None, out_features=None, bias=False):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        
        self.w1 = nn.Linear(in_features, hidden_features, bias=bias)
        self.w2 = nn.Linear(in_features, hidden_features, bias=bias)
        self.w3 = nn.Linear(hidden_features, out_features, bias=bias)
        self.act = nn.SiLU()

    def forward(self, x):
        return self.w3(self.act(self.w1(x)) * self.w2(x))

class RotaryPositionalEmbedding(nn.Module):
    """
    旋转位置编码 (RoPE)
    """
    def __init__(self, dim, max_seq_len=2048):
        super().__init__()
        self.dim = dim
        inv_freq = 1.0 / (10000 ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer("inv_freq", inv_freq)

        t = torch.arange(max_seq_len, device=self.inv_freq.device).type_as(self.inv_freq)
        freqs = torch.einsum("i,j->ij", t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        self.register_buffer("cos_cached", emb.cos()[None, None, :, :])
        self.register_buffer("sin_cached", emb.sin()[None, None, :, :])

    def forward(self, q, k):
        # q, k: [bs, num_heads, seq_len, head_dim]
        seq_len = q.shape[-2]
        cos = self.cos_cached[:, :, :seq_len, ...]
        sin = self.sin_cached[:, :, :seq_len, ...]
        
        def rotate_half(x):
            x1, x2 = x[..., : self.dim // 2], x[..., self.dim // 2 :]
            return torch.cat((-x2, x1), dim=-1)

        q_rotated = (q * cos) + (rotate_half(q) * sin)
        k_rotated = (k * cos) + (rotate_half(k) * sin)
        
        return q_rotated, k_rotated


class EnhancedTransformerBlock(nn.Module):
    """
    集成了RoPE和SwiGLU的增强版Transformer块
    """
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        self.norm1 = RMSNorm(d_model)
        self.attention = MultiHeadAttention(d_model, num_heads, dropout)
        
        self.norm2 = RMSNorm(d_model)
        # 使用SwiGLU替换标准FFN
        self.ffn = SwiGLU(in_features=d_model, hidden_features=d_model * 4, out_features=d_model)
        
        self.dropout = nn.Dropout(dropout)

    def forward(self, x, rope):
        # x: [bs, seq_len, d_model]
        # rope: RoPE模块的实例

        # --- 自注意力部分 ---
        normed_x = self.norm1(x)
        
        # 将输入投影到Q,K,V
        batch_size, seq_len, _ = normed_x.shape
        q = self.attention.q_proj(normed_x).view(batch_size, seq_len, self.attention.num_heads, self.attention.head_dim).transpose(1, 2)
        k = self.attention.k_proj(normed_x).view(batch_size, seq_len, self.attention.num_heads, self.attention.head_dim).transpose(1, 2)
        v = self.attention.v_proj(normed_x).view(batch_size, seq_len, self.attention.num_heads, self.attention.head_dim).transpose(1, 2)
        
        # 应用RoPE
        q, k = rope(q, k)
        
        # 计算注意力
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.attention.scale
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.attention.dropout(attn_weights)
        attn_output = torch.matmul(attn_weights, v)
        
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.attention.d_model
        )
        # --- (修正结束) ---
        
        attn_output = self.attention.out_proj(attn_output)
        
        # 残差连接
        x = x + self.dropout(attn_output)
        
        # --- FFN部分 ---
        ffn_out = self.ffn(self.norm2(x))
        x = x + self.dropout(ffn_out)
        
        return x

class RMSNorm(nn.Module):
    """
    RMS归一化层
    """
    def __init__(self, dim, eps=1e-5):
        """
        初始化RMS归一化层
        
        参数:
            dim: 输入特征维度
            eps: 数值稳定性常数
        """
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def forward(self, x):
        """
        前向传播
        
        参数:
            x: 输入张量 [批大小, 序列长度, 隐藏维度]
            
        返回:
            归一化后的张量
        """
        # 计算RMS
        rms = torch.sqrt(torch.mean(x**2, dim=-1, keepdim=True) + self.eps)
        # 归一化并加权
        return self.weight * x / rms


class InceptionBlock1D(nn.Module):
    """
    1D Inception块，包含多尺度卷积和残差连接。
    """
    def __init__(self, in_channels, bottleneck_scale=0.5, kernel_sizes=[3, 5, 7], dropout=0.1):
        super().__init__()
        
        bottleneck_channels = int(in_channels * bottleneck_scale)
        out_channels_per_branch = in_channels // (len(kernel_sizes) + 1)
        
        # 1. 瓶颈层 (1x1 卷积)
        self.bottleneck = nn.Conv1d(in_channels, bottleneck_channels, kernel_size=1, bias=False)
        
        # 2. 多尺度卷积分支
        self.convs = nn.ModuleList([
            nn.Conv1d(bottleneck_channels, out_channels_per_branch, kernel_size=k, padding='same', bias=False)
            for k in kernel_sizes
        ])
        
        # 3. MaxPooling 分支
        self.pool_proj = nn.Conv1d(in_channels, out_channels_per_branch, kernel_size=1, bias=False)
        
        # 4. 组合后的归一化和激活
        total_out_channels = out_channels_per_branch * (len(kernel_sizes) + 1)
        self.norm = nn.LayerNorm(in_channels) # 使用LayerNorm以适应Transformer
        self.activation = nn.GELU()
        self.dropout = nn.Dropout(dropout)

        # 5. 用于残差连接的1x1卷积，以匹配维度
        if total_out_channels != in_channels:
            self.residual_proj = nn.Conv1d(total_out_channels, in_channels, kernel_size=1, bias=False)
        else:
            self.residual_proj = nn.Identity()

    def forward(self, x):
        # x 的形状: [batch_size, channels, seq_len]
        
        # 保存残差连接的输入
        residual = x
        
        # 瓶颈层
        bottleneck_out = self.activation(self.bottleneck(x))
        
        # 计算所有分支
        branch_outputs = []
        # 多尺度卷积分支
        for conv in self.convs:
            branch_outputs.append(conv(bottleneck_out))
        # MaxPooling 分支
        pool_out = F.max_pool1d(x, kernel_size=3, stride=1, padding=1)
        branch_outputs.append(self.pool_proj(pool_out))
        
        # 沿通道维度拼接所有分支的输出
        concatenated = torch.cat(branch_outputs, dim=1)
        
        # 通过1x1卷积调整维度以匹配残差
        projected = self.residual_proj(concatenated)
        
        # 添加残差连接并应用归一化
        output = self.norm((self.dropout(projected) + residual).permute(0, 2, 1)).permute(0, 2, 1)
        
        return output

class ComplexCNNFeatureExtractor(nn.Module):
    """
    使用多个Inception块的复杂CNN特征提取器
    """
    def __init__(self, input_dim, d_model, num_blocks=2, bottleneck_scale=0.5, kernel_sizes=[3, 5, 7], dropout=0.1):
        super().__init__()
        
        # 初始卷积层，将输入维度映射到d_model
        self.initial_conv = nn.Conv1d(input_dim, d_model, kernel_size=1, bias=False)
        
        # 堆叠多个Inception块
        self.inception_blocks = nn.Sequential(*[
            InceptionBlock1D(
                in_channels=d_model,
                bottleneck_scale=bottleneck_scale,
                kernel_sizes=kernel_sizes,
                dropout=dropout
            ) for _ in range(num_blocks)
        ])
        
        self.final_norm = nn.LayerNorm(d_model)

    def forward(self, x):
        # x 的形状: [batch_size, seq_len, feature_dim]
        # 调整维度以适应Conv1d
        x = x.permute(0, 2, 1)  # -> [batch_size, feature_dim, seq_len]
        
        # 初始投影
        x = self.initial_conv(x) # -> [batch_size, d_model, seq_len]
        
        # 通过Inception块
        x = self.inception_blocks(x) # -> [batch_size, d_model, seq_len]
        
        # 调整回Transformer需要的维度
        x = x.permute(0, 2, 1)  # -> [batch_size, seq_len, d_model]
        
        # 最终归一化
        x = self.final_norm(x)
        
        return x



class FeatureAttention(nn.Module):
    """特征注意力模块，用于动态选择重要特征"""
    def __init__(self, input_dim):
        super().__init__()
        self.attention = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.Linear(input_dim // 2, input_dim),
            nn.Sigmoid()
        )
        self.gate = nn.Parameter(torch.tensor(0.5))
        
    def forward(self, x):
        # x: [batch_size, seq_len, feature_dim]
        batch_size, seq_len, feature_dim = x.shape
        
        # 计算序列级别的特征重要性
        seq_feats = x.mean(dim=1)  # [batch_size, feature_dim]
        weights = self.attention(seq_feats)  # [batch_size, feature_dim]
        
        # 应用门控注意力权重
        gated_weights = self.gate * weights + (1 - self.gate)  # 保留部分原始信息
        
        # 应用注意力权重到所有时间步
        return x * gated_weights.unsqueeze(1)  # [batch_size, seq_len, feature_dim]

class FactorInteractionBlock(nn.Module):
    """建模所有时间步的因子间交互关系"""
    def __init__(self, input_dim, hidden_dim):
        super().__init__()
        self.factor_proj = nn.Linear(input_dim, hidden_dim)
        
        # 时间步注意力
        self.time_attention = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 1)
        )
        
        # 因子交互模块
        self.interaction = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )
        
        self.norm = RMSNorm(hidden_dim)
        self.output_proj = nn.Linear(hidden_dim, 1)
        
    def forward(self, x):
        # x: [batch_size, seq_len, feature_dim]
        batch_size, seq_len, _ = x.shape
        
        # 投影到隐藏空间
        proj = self.factor_proj(x)  # [batch_size, seq_len, hidden_dim]
        
        # 对每个时间步进行因子交互建模
        interactions = []
        for t in range(seq_len):
            step_features = proj[:, t, :]  # [batch_size, hidden_dim]
            step_interaction = self.interaction(step_features)  # [batch_size, hidden_dim]
            enhanced = self.norm(step_features + step_interaction)  # [batch_size, hidden_dim]
            interactions.append(enhanced)
        
        # 堆叠所有时间步的结果
        all_interactions = torch.stack(interactions, dim=1)  # [batch_size, seq_len, hidden_dim]
        
        # 计算时间步注意力权重
        attn_scores = self.time_attention(all_interactions)  # [batch_size, seq_len, 1]
        attn_weights = F.softmax(attn_scores, dim=1)  # [batch_size, seq_len, 1]
        
        # 加权融合
        context = torch.sum(all_interactions * attn_weights, dim=1)  # [batch_size, hidden_dim]
        
        # 输出投影
        out = self.output_proj(context)  # [batch_size, 1]
        
        return out, context

class MultiHeadAttention(nn.Module):
    """多头自注意力机制"""
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
        
    def forward(self, x, mask=None):
        # 注意：这个类的forward方法在新架构下将不再被直接调用。
        # 计算逻辑已移至 EnhancedTransformerBlock 以便集成RoPE。
        # 保留此方法以确保向后兼容或独立使用。
        batch_size, seq_len, d_model = x.shape
        
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        out = torch.matmul(attn_weights, v)
        out = out.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)
        out = self.out_proj(out)
        
        return out



class AdaptiveGraphConvolutionalBlock(nn.Module):
    """
    增强版自适应图卷积块，添加了额外的非线性和归一化层
    """
    def __init__(
        self, 
        d_model, 
        n_features, 
        node_embedding_dim=16, 
        gnn_polynomial_order=5,
        dropout=0.1
    ):
        super().__init__()
        self.d_model = d_model
        self.n_features = n_features
        self.node_embedding_dim = node_embedding_dim
        self.K = gnn_polynomial_order
        
        # 节点嵌入矩阵
        self.node_embeddings = nn.Parameter(torch.randn(n_features, node_embedding_dim))
        
        # 高斯核缩放因子
        self.psi = nn.Parameter(torch.tensor(1.0))
        
        # 过滤器权重生成器
        self.F_w = nn.Parameter(torch.randn(node_embedding_dim, gnn_polynomial_order + 1, d_model))
        self.f_b = nn.Parameter(torch.zeros(node_embedding_dim))
        
        # 输出投影 - 使用两层MLP
        self.output_projection = nn.Sequential(
            nn.Linear(n_features, n_features // 2),
            nn.LayerNorm(n_features // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(n_features // 2, 1)
        )
        
        # 添加边缘注意力
        self.edge_attention = nn.Parameter(torch.ones(n_features, n_features))
        self.attention_temp = nn.Parameter(torch.tensor(1.0))

    def _compute_chebyshev_polynomials(self, A_norm, K):
        # 初始化切比雪夫多项式列表
        polynomials = [None] * (K + 1)
        
        # T_0(x) = I
        polynomials[0] = torch.eye(A_norm.size(0), device=A_norm.device)
        
        # T_1(x) = x
        polynomials[1] = A_norm
        
        # 递归计算高阶多项式
        for k in range(2, K + 1):
            # T_k(x) = 2x·T_(x) - T_(x)
            polynomials[k] = 2 * torch.matmul(A_norm, polynomials[k-1]) - polynomials[k-2]
            
        return polynomials

    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 取序列的最后一个时间步作为图卷积的输入
        x_last = x[:, -1, :]  # [批大小, 特征数]
        
        # 计算节点间距离矩阵
        node_sim = torch.matmul(self.node_embeddings, self.node_embeddings.t())  # [n_features, n_features]
        diag = torch.diag(node_sim)  # [n_features]
        
        # 采用高斯核计算邻接矩阵
        D = diag.unsqueeze(1) + diag.unsqueeze(0) - 2 * node_sim  # [n_features, n_features]
        A = torch.exp(-self.psi * D)  # [n_features, n_features]
        
        # 应用边缘注意力
        A = A * F.softmax(self.edge_attention * self.attention_temp, dim=1)
        
        # 归一化邻接矩阵（按行softmax）
        A_norm = F.softmax(A, dim=1)  # [n_features, n_features]
        
        # 计算切比雪夫多项式
        polynomials = self._compute_chebyshev_polynomials(A_norm, self.K)  # K+1个 [n_features, n_features] 矩阵
        
        # 根据节点嵌入生成过滤器权重和偏置
        W_filter = torch.matmul(self.node_embeddings, self.F_w.reshape(self.node_embedding_dim, -1)).reshape(
            self.n_features, self.K + 1, self.d_model)  # [n_features, K+1, d_model]
        b_filter = torch.matmul(self.node_embeddings, self.f_b)  # [n_features]
        
        # 应用图卷积
        out = torch.zeros(batch_size, self.n_features, device=x.device)
        
        for k in range(self.K + 1):
            # 应用多项式
            poly_x = torch.matmul(x_last, polynomials[k])  # [batch_size, n_features]
            
            # 应用特征特定的权重
            out += torch.sum(poly_x.unsqueeze(-1) * W_filter[:, k, :], dim=-1)  # [batch_size, n_features]
        
        # 添加偏置
        out += b_filter  # [batch_size, n_features]
        
        # 最后的线性投影得到标量输出
        out = self.output_projection(out)  # [batch_size, 1]
        
        return out.squeeze(-1)  # [batch_size]


class DynamicRuleDiscoveryBranch(nn.Module):
    """
    动态规则发现分支（V2 - 简化且更高效）。
    它通过直接比较两组学习到的“复合因子”来自动编码规则，无需手动配置规则数量。
    """
    def __init__(self, input_dim, d_model, num_heads=4, num_layers=2, dropout=0.1):
        super().__init__()
        
        # 1. 投影层：学习将原始因子“混合”成两组不同的复合因子
        # 每组都有 d_model 个复合因子
        self.proj_A = nn.Linear(input_dim, d_model)
        self.proj_B = nn.Linear(input_dim, d_model)
        
        # 2. 激活函数和归一化
        self.activation = nn.Tanh()
        self.norm = nn.LayerNorm(d_model)

        # 3. 时序聚合器：处理“规则特征”的时间序列
        # (这部分保持不变)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, 
            nhead=num_heads,
            dim_feedforward=d_model * 2,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.temporal_aggregator = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 4. [CLS] Token 用于汇总序列信息
        # (这部分保持不变)
        self.cls_token = nn.Parameter(torch.randn(1, 1, d_model))
        
        # 5. 最终预测头
        # (这部分保持不变)
        self.output_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, 1)
        )

    def forward(self, x):
        # x: [batch, seq_len, input_dim]
        batch_size = x.shape[0]
        
        # --- 1. 生成规则特征序列 ---
        
        # 学习两组复合因子
        composite_A = self.proj_A(x)  # -> [batch, seq_len, d_model]
        composite_B = self.proj_B(x)  # -> [batch, seq_len, d_model]
        
        # 直接比较，生成 d_model 个规则特征的时间序列
        rule_features = self.activation(composite_A - composite_B)
        
        # 归一化，稳定Transformer的输入
        rule_features = self.norm(rule_features) # -> [batch, seq_len, d_model]
        
        # --- 2. 时序聚合 ---
        # (这部分逻辑和之前完全一样)
        
        # 添加 [CLS] token
        cls_tokens = self.cls_token.expand(batch_size, -1, -1)
        agg_input = torch.cat([cls_tokens, rule_features], dim=1)
        
        # 通过Transformer来发现规则序列中的模式
        agg_output = self.temporal_aggregator(agg_input)
        
        # 提取 [CLS] token 的最终表示，作为整个序列规则的总结
        context = agg_output[:, 0, :]
        
        # 生成预测
        prediction = self.output_head(context)
        
        return prediction




class SAMBAModel(nn.Module):
    """
    整合CNN-Transformer、GNN、因子交互和动态规则发现的四分支模型
    """
    def __init__(
        self,
        input_dim,
        d_model=config.D_MODEL,
        n_layer=config.N_LAYER,
        num_heads=config.NUM_HEADS,
        gnn_k=config.GNN_K,
        node_embedding_dim=config.NODE_EMBEDDING_DIM,
        dropout=config.DROPOUT,
        cnn_blocks=config.CNN_BLOCKS,
        cnn_kernel_sizes=config.CNN_KERNEL_SIZES,
        cnn_bottleneck_scale=config.CNN_BOTTLENECK_SCALE
    ):
        super().__init__()
        
        # --- 分支 0: 共享的特征注意力 ---
        self.feature_attention = FeatureAttention(input_dim)
        
        # --- 分支 1, 2, 3 (保持不变) ---
        self.cnn_extractor = ComplexCNNFeatureExtractor(input_dim, d_model, cnn_blocks, cnn_bottleneck_scale, cnn_kernel_sizes, dropout)
        assert d_model % 2 == 0, "d_model must be even for RoPE"
        self.rope = RotaryPositionalEmbedding(dim=d_model // num_heads, max_seq_len=config.LOOKBACK_WINDOW * 2)
        self.transformer_blocks = nn.ModuleList([EnhancedTransformerBlock(d_model, num_heads, dropout) for _ in range(n_layer)])
        self.temporal_head = nn.Sequential(nn.LayerNorm(d_model), nn.Linear(d_model, d_model // 2), nn.GELU(), nn.Dropout(dropout), nn.Linear(d_model // 2, 1))
        self.factor_interaction = FactorInteractionBlock(input_dim, d_model)
        self.agc_block = AdaptiveGraphConvolutionalBlock(d_model, input_dim, node_embedding_dim, gnn_k, dropout)
        
        # --- (替换) 分支 4: 动态规则发现分支 ---
        self.rule_branch = DynamicRuleDiscoveryBranch(
            input_dim=input_dim,
            d_model=d_model,
            num_heads=num_heads,
            num_layers=2, # 可以为这个分支设置独立的层数
            dropout=dropout
        )
        
        # --- 最终融合 (权重参数仍为4个) ---
        self.output_weight = nn.Parameter(torch.tensor([0.3, 0.25, 0.25, 0.2]))

    def forward(self, x):
        # x: [batch, seq, input_dim]
        
        x_attended = self.feature_attention(x)
        
        # --- 并行计算四个分支的输出 ---
        
        # 分支 1: CNN-Transformer
        temporal_features = self.cnn_extractor(x_attended)
        for block in self.transformer_blocks:
            temporal_features = block(temporal_features, self.rope)
        temporal_out = self.temporal_head(temporal_features[:, -1, :])
        
        # 分支 2: 因子交互
        factor_out, _ = self.factor_interaction(x_attended)

        # 分支 3: GNN
        agc_out = self.agc_block(x_attended).unsqueeze(1)
        
        # 分支 4: 动态规则发现
        # 同样，建议直接使用原始输入 `x` 来发现规则
        rule_out = self.rule_branch(x)
        
        # --- 最终组合 ---
        weights = F.softmax(self.output_weight, dim=0)
        combined_out = (weights[0] * temporal_out + 
                        weights[1] * factor_out + 
                        weights[2] * agc_out +
                        weights[3] * rule_out)
        
        return combined_out.squeeze(-1)




