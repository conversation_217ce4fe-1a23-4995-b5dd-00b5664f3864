# coding: utf-8
import pandas as pd
import numpy as np
import talib
import datetime
import os
import warnings
from joblib import Parallel, delayed
import multiprocessing
from tqdm.auto import tqdm
from scipy import stats
warnings.filterwarnings('ignore')
warnings.filterwarnings("ignore", category=RuntimeWarning)

# --- 1. 配置参数 ---
CB_BASIC_FILE = 'cb_basic.csv'
CB_DAILY_FILE = 'cb_daily.csv'
STOCK_DAILY_FILE = 'daily.csv'
OUTPUT_FILE = 'cb_factors.csv'

# 计算参数
ATR_WINDOW = 14
MA_WINDOWS = [5, 10, 20]
BIAS_WINDOWS = [10, 20]
BB_WINDOW = 20
BB_STD = 2
MIN_OBS_FOR_CALC = 60
N_JOBS = multiprocessing.cpu_count() - 1


# --- 2. 数据加载与预处理函数 ---
def load_and_preprocess_data(cb_basic_file, cb_daily_file, stock_daily_file):
    """加载所有CSV文件并进行初步预处理"""
    try:
        cb_basic = pd.read_csv(cb_basic_file, encoding='utf-8')
        cb_daily = pd.read_csv(cb_daily_file, encoding='utf-8')
        stock_daily = pd.read_csv(stock_daily_file, encoding='utf-8')
    except FileNotFoundError as e:
        print(f"错误：找不到文件 {e.filename}。请确保所有CSV文件都在指定路径下。")
        return None, None, None, None, None

    # --- 数据清洗和类型转换 ---
    # 可转债基础信息
    cb_basic['maturity_date'] = pd.to_datetime(cb_basic['maturity_date'], format='%Y%m%d')
    cb_basic['conv_start_date'] = pd.to_datetime(cb_basic['conv_start_date'], format='%Y%m%d', errors='coerce') 
    cb_basic['conv_end_date'] = pd.to_datetime(cb_basic['conv_end_date'], format='%Y%m%d', errors='coerce')
    cb_basic['list_date'] = pd.to_datetime(cb_basic['list_date'], format='%Y%m%d', errors='coerce')
    cb_basic['delist_date'] = pd.to_datetime(cb_basic['delist_date'], format='%Y%m%d', errors='coerce')

    
    # 只保留未摘牌的可转债基本信息
    cb_basic = cb_basic[cb_basic['delist_date'].isna()]
    cb_basic = cb_basic[['ts_code', 'stk_code', 'par', 'conv_start_date', 'conv_end_date', 'coupon_rate']]
    cb_basic = cb_basic.drop_duplicates(subset=['ts_code']) # 防止重复

    # 可转债日行情
    cb_daily['trade_date'] = pd.to_datetime(cb_daily['trade_date'], format='%Y%m%d')
    # 确保价格列是数值类型
    price_cols_cb = ['pre_close', 'open', 'high', 'low', 'close', 'bond_value', 'cb_value']
    for col in price_cols_cb:
        cb_daily[col] = pd.to_numeric(cb_daily[col], errors='coerce')
    


    cb_daily = cb_daily.sort_values(by=['ts_code', 'trade_date'])
    cb_daily = cb_daily.dropna(subset=['ts_code', 'trade_date', 'close']) # 关键数据不能为空

    # 股票日行情
    stock_daily['trade_date'] = pd.to_datetime(stock_daily['trade_date'], format='%Y%m%d')
    price_cols_stk = ['open', 'high', 'low', 'close', 'pre_close']
    for col in price_cols_stk:
        stock_daily[col] = pd.to_numeric(stock_daily[col], errors='coerce')
    stock_daily = stock_daily.sort_values(by=['ts_code', 'trade_date'])
    stock_daily = stock_daily.dropna(subset=['ts_code', 'trade_date', 'close'])
    # 重命名股票列以区分
    stock_daily = stock_daily.rename(columns={
        'ts_code': 'stk_code',
        'open': 'stk_open', 'high': 'stk_high', 'low': 'stk_low',
        'close': 'stk_close', 'pre_close': 'stk_pre_close',
        'change': 'stk_change', 'pct_chg': 'stk_pct_chg',
        'vol': 'stk_vol', 'amount': 'stk_amount'
    })
    # 保留更多股票列，用于更丰富的计算
    stock_daily = stock_daily[['stk_code', 'trade_date', 'stk_close', 'stk_pre_close', 'stk_open', 'stk_high', 'stk_low', 'stk_vol', 'stk_amount']]

    # --- 合并数据 ---
    cb_merged = pd.merge(cb_daily, cb_basic, on='ts_code', how='left')  # 基本信息
    cb_merged = pd.merge(cb_merged, stock_daily, on=['stk_code', 'trade_date'], how='left')  # 股票行情
    cb_merged = cb_merged.sort_values(by=['ts_code', 'trade_date'])  # 排序

    return cb_basic, cb_daily, stock_daily, cb_merged



# --- 3. 动量类因子 ---
def calculate_momentum(close, window):
    """计算动量因子"""
    return (close / close.shift(window) - 1) * 100

def calculate_momentum_score(momentum_5d, momentum_10d, momentum_20d):
    """动量综合评分"""
    score = (momentum_5d * 0.5 + momentum_10d * 0.3 + momentum_20d * 0.2)
    return score / (np.abs(score).rolling(window=20).mean() + 1e-10)

def calculate_momentum_consistency(close, windows=[5, 10, 20]):
    """动量一致性"""
    momentums = [calculate_momentum(close, w) for w in windows]
    signs = [np.sign(m) for m in momentums]
    consistency = pd.concat(signs, axis=1).sum(axis=1) / len(windows)
    return consistency

def calculate_acceleration(close, window=5):
    """价格加速度"""
    momentum = calculate_momentum(close, window)
    return momentum - momentum.shift(window)

def calculate_momentum_divergence(close, volume, window=10):
    """价格量能背离"""
    price_momentum = calculate_momentum(close, window)
    volume_momentum = calculate_momentum(volume, window)
    return price_momentum - volume_momentum

def calculate_low_vol_momentum(close, volume, window=20):
    """低波动动量"""
    returns = close.pct_change()
    volatility = returns.rolling(window=window).std()
    momentum = calculate_momentum(close, window)
    vol_rank = volatility.rolling(window=window).rank(pct=True)
    return momentum * (1 - vol_rank)

# --- 4. 波动率类因子 ---
def calculate_atr(high, low, close, window=14, volume=None):
    """优化的ATR计算"""
    high_low = high - low
    high_close = np.abs(high - close.shift(1))
    low_close = np.abs(low - close.shift(1))
    
    tr = pd.DataFrame({
        'hl': high_low,
        'hc': high_close,
        'lc': low_close
    }).max(axis=1)
    
    if volume is not None and not volume.isnull().all():
        normalized_volume = volume / volume.rolling(window=window).mean()
        normalized_volume = normalized_volume.clip(0.2, 5)
        tr = tr * normalized_volume
    
    tr_array = np.array(tr.fillna(0)).astype(float)
    first_smooth = talib.EMA(tr_array, timeperiod=window)
    atr = talib.EMA(first_smooth, timeperiod=max(int(window/2), 1))
    
    return atr

def calculate_natr(high, low, close, window=ATR_WINDOW):
    """计算NATR"""
    natr = talib.NATR(high, low, close, timeperiod=window)
    return natr


def calculate_volatility_decay(close, window=20):
    """波动率衰减"""
    returns = close.pct_change()
    weights = np.exp(-np.arange(window) / (window / 2))
    weights = weights / weights.sum()
    
    decay_vol = pd.Series(index=close.index, dtype=float)
    for i in range(window, len(returns)):
        decay_vol.iloc[i] = np.sqrt(np.sum(weights * (returns.iloc[i-window:i].values ** 2)))
    
    return decay_vol

def calculate_volatility_regime(close, short_window=10, long_window=30):
    """波动率体制"""
    returns = close.pct_change()
    short_vol = returns.rolling(window=short_window).std()
    long_vol = returns.rolling(window=long_window).std()
    return short_vol / (long_vol + 1e-10)


def calculate_range_expansion(high, low, close, window=20):
    """范围扩张"""
    daily_range = (high - low) / close
    avg_range = daily_range.rolling(window=window).mean()
    return daily_range / (avg_range + 1e-10)

# --- 5. 趋势类因子 ---
def calculate_ma(close, window):
    """移动平均"""
    return close.rolling(window=window).mean()

def calculate_bias(close, ma):
    """乖离率"""
    return (close - ma) / ma * 100

def calculate_trend_power(close, ma_5, ma_10, ma_20):
    """趋势强度"""
    trend_alignment = ((close > ma_5).astype(int) + 
                      (ma_5 > ma_10).astype(int) + 
                      (ma_10 > ma_20).astype(int)) / 3
    
    ma_spread = (ma_5 - ma_20) / ma_20 * 100
    return trend_alignment * np.abs(ma_spread)

def calculate_early_trend_recognition(close, volume, window=10):
    """早期趋势识别"""
    price_change = close.pct_change(window)
    volume_ratio = volume / volume.rolling(window=window).mean()
    
    # 价格突破配合放量
    breakout_signal = (np.abs(price_change) > price_change.rolling(window=20).std() * 2) & (volume_ratio > 1.5)
    
    # 趋势延续概率
    future_momentum = close.shift(-window) / close - 1
    trend_quality = pd.Series(index=close.index, dtype=float)
    
    for i in range(window, len(close) - window):
        if breakout_signal.iloc[i]:
            trend_quality.iloc[i] = np.sign(price_change.iloc[i]) * future_momentum.iloc[i]
    
    return trend_quality.fillna(0)

# --- 6. 技术指标类因子 ---
def calculate_rsi(close, window=14):
    """RSI指标"""
    return talib.RSI(close, timeperiod=window)

def calculate_rsi_divergence(close, rsi, window=20):
    """RSI背离"""
    price_trend = (close - close.shift(window)) / close.shift(window)
    rsi_trend = (rsi - rsi.shift(window)) / 50
    return price_trend - rsi_trend

def calculate_volume_weighted_rsi(close, volume, window=14):
    """成交量加权RSI"""
    delta = close.diff()
    vol_delta = delta * volume
    
    gain = vol_delta.where(vol_delta > 0, 0)
    loss = -vol_delta.where(vol_delta < 0, 0)
    
    avg_gain = gain.rolling(window=window).mean()
    avg_loss = loss.rolling(window=window).mean()
    
    rs = avg_gain / (avg_loss + 1e-10)
    vw_rsi = 100 - (100 / (1 + rs))
    
    return vw_rsi

def calculate_kdj(high, low, close, n=9, m1=3, m2=3):
    """KDJ指标"""
    low_list = low.rolling(window=n).min()
    high_list = high.rolling(window=n).max()
    
    rsv = (close - low_list) / (high_list - low_list + 1e-10) * 100
    k = rsv.ewm(com=m1-1, adjust=False).mean()
    d = k.ewm(com=m2-1, adjust=False).mean()
    j = 3 * k - 2 * d
    
    return k, d, j

def calculate_bb_indicators(close, window=20, num_std=2):
    """布林带指标"""
    ma = close.rolling(window=window).mean()
    std = close.rolling(window=window).std()
    
    upper_band = ma + num_std * std
    lower_band = ma - num_std * std
    
    bb_position = (close - lower_band) / (upper_band - lower_band + 1e-10)
    bb_width = (upper_band - lower_band) / ma
    
    return bb_position, bb_width

def calculate_price_efficiency(close, window=20):
    """价格效率"""
    net_change = np.abs(close - close.shift(window))
    total_variation = np.abs(close.diff()).rolling(window=window).sum()
    return net_change / (total_variation + 1e-10)

def calculate_macd_indicators(close):
    """MACD指标"""
    macd, signal, hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
    
    # MACD交叉信号
    macd_cross_signal = ((macd > signal) & (macd.shift(1) <= signal.shift(1))).astype(int) - \
                       ((macd < signal) & (macd.shift(1) >= signal.shift(1))).astype(int)
    
    # MACD零轴交叉
    macd_zero_cross = ((macd > 0) & (macd.shift(1) <= 0)).astype(int) - \
                      ((macd < 0) & (macd.shift(1) >= 0)).astype(int)
    
    return macd_cross_signal, macd_zero_cross, hist

def calculate_adx_indicators(high, low, close, window=14):
    """ADX指标组"""
    adx = talib.ADX(high, low, close, timeperiod=window)
    plus_di = talib.PLUS_DI(high, low, close, timeperiod=window)
    minus_di = talib.MINUS_DI(high, low, close, timeperiod=window)
    di_diff = plus_di - minus_di
    
    return adx, plus_di, minus_di, di_diff

def calculate_roc(close, window=10):
    """变化率指标"""
    return talib.ROC(close, timeperiod=window)

# --- 7. 成交量类因子 ---
def calculate_obv_indicators(close, volume):
    """OBV指标组"""
    obv = talib.OBV(close, volume)
    obv_trend = obv.rolling(window=20).apply(lambda x: np.polyfit(range(len(x)), x, 1)[0])
    return obv, obv_trend

def calculate_mfi_indicators(high, low, close, volume, window=14):
    """资金流指标"""
    mfi = talib.MFI(high, low, close, volume, timeperiod=window)
    
    # MFI背离
    price_trend = (close - close.shift(window)) / close.shift(window)
    mfi_trend = (mfi - mfi.shift(window)) / 50
    mfi_divergence = price_trend - mfi_trend
    
    return mfi, mfi_divergence

def calculate_smart_money(close, volume, high, low):
    """聪明钱指标"""
    # 收盘位置
    close_position = (close - low) / (high - low + 1e-10)
    
    # 成交量分布
    smart_volume = volume * (2 * close_position - 1)
    smart_money = smart_volume.rolling(window=20).sum() / volume.rolling(window=20).sum()
    
    return smart_money

def calculate_volume_profile(close, volume, window=20):
    """成交量分布"""
    price_levels = pd.qcut(close.rolling(window=window).mean(), q=10, duplicates='drop')
    volume_by_level = volume.groupby(price_levels, observed=False).transform('sum')
    volume_concentration = volume / (volume_by_level + 1e-10)
    return volume_concentration

def calculate_vp_momentum_penalty(close, volume, window=20):
    """成交量动量惩罚"""
    momentum = calculate_momentum(close, window)
    volume_ratio = volume / volume.rolling(window=window).mean()
    
    # 高动量低成交量惩罚
    penalty = momentum * (1 / (volume_ratio + 0.5))
    return penalty

# --- 8. K线形态类因子 ---
def calculate_candle_patterns(open_price, high, low, close, window=5):
    """K线形态因子"""
    # 收盘位置
    close_position = (close - low) / (high - low + 1e-10)
    
    # 上影线
    upper_shadow = (high - np.maximum(open_price, close)) / (high - low + 1e-10)
    
    # 下影线
    lower_shadow = (np.minimum(open_price, close) - low) / (high - low + 1e-10)
    
    # 实体比例
    body_ratio = np.abs(close - open_price) / (high - low + 1e-10)
    
    # 阳线比例
    candle_color_ratio = (close > open_price).rolling(window=window).mean()
    
    # 滚动窗口统计
    close_position_ma = close_position.rolling(window=window).mean()
    upper_shadow_ma = upper_shadow.rolling(window=window).mean()
    lower_shadow_ma = lower_shadow.rolling(window=window).mean()
    body_ratio_ma = body_ratio.rolling(window=window).mean()
    
    return (close_position_ma, upper_shadow_ma, lower_shadow_ma, 
            body_ratio_ma, candle_color_ratio)

# --- 9. 市场微观结构类因子 ---
def calculate_vwap(close, volume):
    """成交量加权平均价"""
    return (close * volume).rolling(window=20).sum() / volume.rolling(window=20).sum()

def calculate_cost_distribution(close, volume, vwap):
    """成本分布因子"""
    # 平均成本
    avg_cost = vwap
    
    # 高成本区比例
    high_cost_threshold = avg_cost * 1.05
    high_cost_ratio = ((close > high_cost_threshold) * volume).rolling(window=20).sum() / \
                      volume.rolling(window=20).sum()
    
    # 获利比例
    profit_ratio = (close > avg_cost).rolling(window=20).mean()
    
    # 筹码集中度
    price_std = close.rolling(window=20).std()
    concentration = 1 / (price_std / close + 1e-10)
    
    return avg_cost, high_cost_ratio, profit_ratio, concentration

def calculate_fractal_indicator(high, low, window=5):
    """分形指标"""
    # 上分形
    up_fractal = pd.Series(0, index=high.index)
    for i in range(window, len(high) - window):
        if high.iloc[i] == high.iloc[i-window:i+window+1].max():
            up_fractal.iloc[i] = 1
    
    # 下分形
    down_fractal = pd.Series(0, index=low.index)
    for i in range(window, len(low) - window):
        if low.iloc[i] == low.iloc[i-window:i+window+1].min():
            down_fractal.iloc[i] = 1
    
    # 分形强度
    fractal_indicator = up_fractal - down_fractal
    return fractal_indicator.rolling(window=window).sum()

def calculate_swing_strength(high, low, close, window=20):
    """摆动强度"""
    swing_high = high.rolling(window=window).max()
    swing_low = low.rolling(window=window).min()
    swing_range = swing_high - swing_low
    current_position = (close - swing_low) / (swing_range + 1e-10)
    return current_position * swing_range / close

def calculate_high_low_position(high, low, close, window=20):
    """高低点位置"""
    highest = high.rolling(window=window).max()
    lowest = low.rolling(window=window).min()
    return (close - lowest) / (highest - lowest + 1e-10)

def calculate_reversal_indicators(high, low, close, volume, window=20):
    """反转指标"""
    # 顶部反转风险
    high_position = (close - low.rolling(window=window).min()) / \
                    (high.rolling(window=window).max() - low.rolling(window=window).min() + 1e-10)
    volume_decay = volume / volume.rolling(window=window).mean()
    top_reversal_risk = high_position * (1 / (volume_decay + 0.5))
    
    # 底部反转机会
    low_position = 1 - high_position
    volume_surge = volume_decay
    bottom_reversal_chance = low_position * volume_surge
    
    return top_reversal_risk, bottom_reversal_chance

def calculate_mean_reversion(close, window=20):
    """均值回归"""
    ma = close.rolling(window=window).mean()
    std = close.rolling(window=window).std()
    z_score = (close - ma) / (std + 1e-10)
    return -z_score  # 负值表示回归趋势

def calculate_hidden_divergence(close, volume, window=14):
    """隐藏背离"""
    # 价格创新高但成交量萎缩
    price_high = close == close.rolling(window=window).max()
    volume_shrink = volume < volume.rolling(window=window).mean() * 0.8
    hidden_divergence = (price_high & volume_shrink).astype(int)
    return hidden_divergence.rolling(window=5).sum()

def calculate_failure_swing(close, window=20):
    """失败摆动"""
    # 检测假突破
    recent_high = close.rolling(window=window).max()
    recent_low = close.rolling(window=window).min()
    
    # 向上假突破
    up_failure = ((close > recent_high.shift(1)) & 
                  (close.shift(-1) < recent_high)).astype(int)
    
    # 向下假突破
    down_failure = ((close < recent_low.shift(1)) & 
                    (close.shift(-1) > recent_low)).astype(int)
    
    return down_failure - up_failure

# --- 10. 套利类因子 ---
def calculate_bond_equity_ratio(cb_value, bond_value):
    """债股比例"""
    return bond_value / (cb_value + 1e-10)

def calculate_arbitrage_window(close, cb_value, cb_over_rate):
    """套利窗口"""
    # 转股溢价率的反向指标
    arbitrage_opportunity = -cb_over_rate
    
    # 考虑流动性因素
    price_volatility = close.pct_change().rolling(window=20).std()
    arbitrage_window = arbitrage_opportunity / (price_volatility + 0.01)
    
    return arbitrage_window

def calculate_extreme_factor(close, volume, cb_over_rate):
    """极值因子"""
    # 价格极值
    price_percentile = close.rolling(window=60).rank(pct=True)
    
    # 成交量极值
    volume_percentile = volume.rolling(window=60).rank(pct=True)
    
    # 溢价率极值
    premium_percentile = cb_over_rate.rolling(window=60).rank(pct=True)
    
    # 综合极值因子
    extreme_factor = (price_percentile * 0.4 + 
                     volume_percentile * 0.3 + 
                     premium_percentile * 0.3)
    
    return extreme_factor

# --- 11. 股票联动因子 ---
def calculate_stock_momentum(stk_close, windows=[5, 10, 20]):
    """股票动量"""
    momentums = {}
    for w in windows:
        momentums[f'stk_momentum_{w}d'] = calculate_momentum(stk_close, w)
    return momentums

def calculate_return_diff(cb_close, stk_close, window=5):
    """收益率差异"""
    cb_return = cb_close.pct_change(window)
    stk_return = stk_close.pct_change(window, fill_method=None)
    return cb_return - stk_return

# --- 12. 高级策略因子 ---
def calculate_anti_chasing(close, volume, window=10):
    """反追高因子"""
    # 短期涨幅
    short_return = close.pct_change(window)
    
    # 成交量异常
    volume_spike = volume / volume.rolling(window=window).mean()
    
    # 反追高得分
    anti_chasing = -short_return * volume_spike
    return anti_chasing

def calculate_key_level_breakthrough(close, high, low, window=20):
    """关键位突破"""
    # 识别关键阻力位和支撑位
    resistance = high.rolling(window=window).max()
    support = low.rolling(window=window).min()
    
    # 突破强度
    breakthrough_up = (close - resistance.shift(1)) / resistance.shift(1)
    breakthrough_down = (support.shift(1) - close) / support.shift(1)
    
    breakthrough = pd.Series(index=close.index, dtype=float)
    breakthrough[breakthrough_up > 0] = breakthrough_up[breakthrough_up > 0]
    breakthrough[breakthrough_down > 0] = -breakthrough_down[breakthrough_down > 0]
    
    return breakthrough

def calculate_pressure_release(close, volume, high, window=20):
    """压力释放"""
    # 上方套牢盘压力
    trapped_level = high.rolling(window=window).max()
    pressure = (trapped_level - close) / close
    
    # 成交量释放
    volume_release = volume / volume.rolling(window=window).mean()
    
    # 压力释放指标
    pressure_release = pressure * (1 / (volume_release + 0.5))
    return pressure_release

def calculate_accumulation_signal(close, volume, low, window=20):
    """吸筹信号"""
    # 价格低位
    price_position = (close - low.rolling(window=window).min()) / \
                    (close.rolling(window=window).max() - low.rolling(window=window).min() + 1e-10)
    
    # 成交量温和放大
    volume_ratio = volume / volume.rolling(window=window).mean()
    moderate_volume = (volume_ratio > 1.2) & (volume_ratio < 2.0)
    
    # 吸筹得分
    accumulation = (1 - price_position) * moderate_volume.astype(int) * volume_ratio
    return accumulation

def calculate_pullback_opportunity(close, window=20):
    """回调机会"""
    # 趋势强度
    trend = (close - close.rolling(window=window).mean()) / close.rolling(window=window).std()
    
    # 短期回调
    short_pullback = (close.rolling(window=5).max() - close) / close
    
    # 回调机会得分
    pullback_opportunity = trend * short_pullback
    return pullback_opportunity

def calculate_continuation_pattern(close, volume, window=10):
    """延续形态"""
    # 趋势方向
    trend_direction = np.sign(close - close.shift(window))
    
    # 成交量收缩
    volume_contraction = volume < volume.rolling(window=window).mean() * 0.8
    
    # 延续形态得分
    continuation = trend_direction * volume_contraction.astype(int)
    return continuation.rolling(window=5).sum()

def calculate_volume_price_confirmation(close, volume, window=10):
    """量价确认"""
    # 价格变化
    price_change = close.pct_change(window)
    
    # 成交量变化
    volume_change = (volume - volume.shift(window)) / volume.shift(window)
    
    # 量价同向确认
    confirmation = np.sign(price_change) * np.sign(volume_change)
    return confirmation.rolling(window=5).mean()

def calculate_momentum_quality(close, volume, high, low, window=20):
    """动量质量"""
    # 基础动量
    momentum = calculate_momentum(close, window)
    
    # 成交量支持
    volume_support = volume / volume.rolling(window=window).mean()
    
    # 价格效率
    efficiency = calculate_price_efficiency(close, window)
    
    # 动量质量综合得分
    quality = momentum * volume_support * efficiency
    return quality / (np.abs(quality).rolling(window=20).mean() + 1e-10)

def calculate_trend_confirmation_index(close, volume, window=20):
    """趋势确认指数"""
    # 价格趋势
    price_trend = (close - close.rolling(window=window).mean()) / close.rolling(window=window).std()
    
    # 成交量趋势
    volume_trend = (volume - volume.rolling(window=window).mean()) / volume.rolling(window=window).std()
    
    # OBV趋势
    obv = talib.OBV(close, volume)
    obv_trend = (obv - obv.rolling(window=window).mean()) / obv.rolling(window=window).std()
    
    # 综合确认指数
    confirmation_index = (price_trend + volume_trend + obv_trend) / 3
    return confirmation_index

def calculate_volatility_adjusted_momentum(close, window=20):
    """波动率调整动量"""
    momentum = calculate_momentum(close, window)
    volatility = close.pct_change().rolling(window=window).std()
    
    # 波动率标准化
    vol_percentile = volatility.rolling(window=60).rank(pct=True)
    adjustment_factor = 1 - vol_percentile * 0.5
    
    return momentum * adjustment_factor

def calculate_chande_momentum_oscillator(close, window=14):
    """钱德动量振荡器"""
    gains = close.diff().clip(lower=0)
    losses = -close.diff().clip(upper=0)
    
    sum_gains = gains.rolling(window=window).sum()
    sum_losses = losses.rolling(window=window).sum()
    
    cmo = 100 * (sum_gains - sum_losses) / (sum_gains + sum_losses + 1e-10)
    return cmo

def calculate_advanced_breakout_score(close, volume, high, low, window=20):
    """高级突破得分"""
    
    
    # 价格突破
    price_breakout = (close - high.rolling(window=window).max().shift(1)) / close
    
    # 成交量突破
    volume_breakout = volume / volume.rolling(window=window).max()
    
    # ATR突破
    atr = calculate_atr(high, low, close, window)
    
    # 将 atr 转换为 pandas Series，保持与其他变量一致的索引
    if isinstance(atr, np.ndarray):
        atr = pd.Series(atr, index=close.index)
    
    atr_breakout = atr / atr.rolling(window=window).mean()
    
    # 综合突破得分
    breakout_score = (price_breakout * 0.5 + volume_breakout * 0.3 + atr_breakout * 0.2)
    
    return breakout_score
def calculate_trend_stability_index(close, window=20):
    """趋势稳定性指数"""
    # 线性回归斜率
    def linear_regression_slope(x):
        if len(x) < 2:
            return 0
        return np.polyfit(range(len(x)), x, 1)[0]
    
    # 计算斜率
    slopes = close.rolling(window=window).apply(linear_regression_slope)
    
    # 计算R平方
    def r_squared(x):
        if len(x) < 2:
            return 0
        y = np.array(x)
        x = np.arange(len(y))
        slope, intercept = np.polyfit(x, y, 1)
        y_pred = slope * x + intercept
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        return 1 - (ss_res / (ss_tot + 1e-10))
    
    r2 = close.rolling(window=window).apply(r_squared)
    
    # 趋势稳定性 = 斜率标准化 * R平方
    stability_index = slopes / (close.rolling(window=window).std() + 1e-10) * r2
    return stability_index

def calculate_pattern_recognition_score(open_price, high, low, close, window=20):
    """形态识别得分"""
    # 简化的形态识别
    body_size = np.abs(close - open_price)
    upper_shadow = high - np.maximum(close, open_price)
    lower_shadow = np.minimum(close, open_price) - low
    
    # 锤子线
    hammer = ((lower_shadow > body_size * 2) & 
              (upper_shadow < body_size * 0.5) & 
              (close > open_price)).astype(int)
    
    # 倒锤子
    inverted_hammer = ((upper_shadow > body_size * 2) & 
                       (lower_shadow < body_size * 0.5) & 
                       (close < open_price)).astype(int)
    
    # 十字星
    doji = (body_size < (high - low) * 0.1).astype(int)
    
    # 吞没形态
    bullish_engulfing = ((close > open_price) & 
                         (open_price < close.shift(1)) & 
                         (close > open_price.shift(1))).astype(int)
    
    bearish_engulfing = ((close < open_price) & 
                         (open_price > close.shift(1)) & 
                         (close < open_price.shift(1))).astype(int)
    
    # 综合得分
    pattern_score = (hammer - inverted_hammer + 
                    doji * 0.5 + 
                    bullish_engulfing * 2 - 
                    bearish_engulfing * 2)
    
    return pattern_score.rolling(window=5).sum()

def calculate_support_resistance_zones(high, low, close, window=20):
    """支撑阻力区域"""
    # 识别关键价格水平
    pivot = (high + low + close) / 3
    
    # 支撑位
    support1 = 2 * pivot - high
    support2 = pivot - (high - low)
    
    # 阻力位
    resistance1 = 2 * pivot - low
    resistance2 = pivot + (high - low)
    
    # 当前价格相对位置
    if len(close) > 0:
        current_close = close.iloc[-1]
        
        zone_score = pd.Series(index=close.index, dtype=float)
        zone_score[close < support2] = -2  # 强支撑区
        zone_score[(close >= support2) & (close < support1)] = -1  # 支撑区
        zone_score[(close >= support1) & (close < pivot)] = -0.5  # 弱支撑区
        zone_score[(close >= pivot) & (close < resistance1)] = 0.5  # 弱阻力区
        zone_score[(close >= resistance1) & (close < resistance2)] = 1  # 阻力区
        zone_score[close >= resistance2] = 2  # 强阻力区
        
        return zone_score.rolling(window=5).mean()
    
    return pd.Series(0, index=close.index)

def calculate_intraday_strength(open_price, high, low, close):
    """日内强度"""
    # 日内位置
    intraday_position = (close - low) / (high - low + 1e-10)
    
    # 开盘相对强度
    open_strength = (close - open_price) / (high - low + 1e-10)
    
    # 综合日内强度
    intraday_strength = intraday_position * 0.7 + open_strength * 0.3
    return intraday_strength.rolling(window=5).mean()

def calculate_trend_regression_factor(close, window=20):
    """趋势回归因子"""
    def trend_strength(x):
        if len(x) < 2:
            return 0
        slope, intercept = np.polyfit(range(len(x)), x, 1)
        predicted = slope * (len(x) - 1) + intercept
        actual = x.iloc[-1]
        return (actual - predicted) / (np.std(x) + 1e-10)
    
    return close.rolling(window=window).apply(trend_strength)

def calculate_gap_analysis_factor(open_price, close, low, high):
    """跳空分析因子"""
    # 跳空大小
    gap = open_price - close.shift(1)
    gap_ratio = gap / close.shift(1)
    
    # 跳空填补
    gap_filled = pd.Series(0, index=close.index)
    
    for i in range(1, len(close)):
        if gap_ratio.iloc[i] > 0.01:  # 向上跳空
            if low.iloc[i] <= close.iloc[i-1]:  # 已填补
                gap_filled.iloc[i] = -1
            else:
                gap_filled.iloc[i] = 1
        elif gap_ratio.iloc[i] < -0.01:  # 向下跳空
            if high.iloc[i] >= close.iloc[i-1]:  # 已填补
                gap_filled.iloc[i] = 1
            else:
                gap_filled.iloc[i] = -1
    
    return gap_filled.rolling(window=5).sum()

def calculate_opening_range_breakout(open_price, high, low, close, window=5):
    """开盘区间突破"""
    # 开盘区间
    opening_high = high.rolling(window=window).max()
    opening_low = low.rolling(window=window).min()
    opening_range = opening_high - opening_low
    
    # 突破信号
    breakout_up = (close > opening_high.shift(1)).astype(int)
    breakout_down = (close < opening_low.shift(1)).astype(int)
    
    # 突破强度
    breakout_strength = ((close - opening_high.shift(1)) * breakout_up - 
                        (opening_low.shift(1) - close) * breakout_down) / (opening_range + 1e-10)
    
    return breakout_strength

def calculate_momentum_divergence_factor(close, volume, high, low, window=14):
    """动量背离因子"""
    # 价格动量
    price_momentum = calculate_momentum(close, window)
    
    # 成交量动量
    volume_momentum = calculate_momentum(volume, window)
    
    # RSI背离
    rsi = calculate_rsi(close, window)
    rsi_divergence = calculate_rsi_divergence(close, rsi, window)
    
    # MACD背离
    macd, signal, hist = talib.MACD(close)
    macd_divergence = (close - close.shift(window)) / close.shift(window) - \
                      (macd - macd.shift(window)) / (np.abs(macd.shift(window)) + 1e-10)
    
    # 综合背离因子
    divergence_factor = (price_momentum - volume_momentum) * 0.3 + \
                       rsi_divergence * 0.3 + \
                       macd_divergence * 0.4
    
    return divergence_factor

def calculate_volume_spike_reversal(close, volume, window=20):
    """放量反转"""
    # 成交量激增
    volume_spike = volume / volume.rolling(window=window).mean() > 2
    
    # 价格反转
    price_reversal = (close < close.shift(1)).astype(int)
    
    # 放量反转信号
    spike_reversal = volume_spike & price_reversal
    
    return spike_reversal.rolling(window=5).sum()

def calculate_volume_price_trend(close, volume):
    """量价趋势"""
    # VPT指标
    vpt = ((close - close.shift(1)) / close.shift(1) * volume).cumsum()
    
    # VPT动量
    vpt_momentum = (vpt - vpt.shift(20)) / (np.abs(vpt.shift(20)) + 1e-10)
    
    return vpt_momentum

# --- 13. 防追高因子组 ---
def calculate_anti_fomo_index(close, volume, window=10):
    """反FOMO指数"""
    # 短期涨幅
    short_gain = close.pct_change(window)
    
    # 成交量异常程度
    volume_abnormal = volume / volume.rolling(window=window*2).mean()
    
    # 价格偏离度
    ma = close.rolling(window=window*2).mean()
    price_deviation = (close - ma) / ma
    
    # 反FOMO综合指数
    anti_fomo = -(short_gain * volume_abnormal * price_deviation)
    
    return anti_fomo

def calculate_exhaustion_indicator(close, volume, high, window=20):
    """衰竭指标"""
    # 价格动能衰竭
    momentum_decay = calculate_momentum(close, 5) / calculate_momentum(close, 20)
    
    # 成交量衰竭
    volume_decay = volume.rolling(window=5).mean() / volume.rolling(window=20).mean()
    
    # 高点衰竭
    high_decay = (high.rolling(window=5).max() - high) / high
    
    # 综合衰竭指标
    exhaustion = momentum_decay * volume_decay * (1 + high_decay)
    
    return exhaustion

def calculate_overbought_oscillator(close, volume, window=14):
    """超买振荡器"""
    # RSI超买
    rsi = calculate_rsi(close, window)
    rsi_overbought = (rsi - 70) / 30  # 标准化到0-1
    
    # 价格位置超买
    price_position = (close - close.rolling(window=window*2).min()) / \
                    (close.rolling(window=window*2).max() - close.rolling(window=window*2).min() + 1e-10)
    
    # 成交量超买
    volume_ratio = volume / volume.rolling(window=window*2).mean()
    
    # 综合超买指标
    overbought = (rsi_overbought * 0.5 + 
                 price_position * 0.3 + 
                 np.clip(volume_ratio - 1, 0, 1) * 0.2)
    
    return overbought

def calculate_rush_index(close, volume, window=5):
    """急涨指数"""
    # 价格急涨
    price_rush = close.pct_change(window)
    
    # 成交量急增
    volume_rush = volume / volume.rolling(window=window*4).mean()
    
    # 加速度
    acceleration = price_rush - price_rush.shift(window)
    
    # 急涨综合指数
    rush_index = price_rush * volume_rush * (1 + acceleration)
    
    return rush_index

def calculate_price_thrust_control(close, high, low, window=10):
    """价格推进控制"""
    # 推进幅度
    thrust = (close - close.shift(window)) / close.shift(window)
    
    # 振幅控制
    true_range = high - low
    avg_range = true_range.rolling(window=window*2).mean()
    range_expansion = true_range / (avg_range + 1e-10)
    
    # 推进质量
    thrust_quality = thrust / (range_expansion + 1)
    
    return thrust_quality

def calculate_retracement_opportunity(close, high, window=20):
    """回撤机会"""
    # 从高点回撤幅度
    rolling_high = high.rolling(window=window).max()
    retracement = (rolling_high - close) / rolling_high
    
    # 趋势强度
    trend_strength = (close - close.rolling(window=window*2).mean()) / \
                    close.rolling(window=window*2).std()
    
    # 回撤机会得分
    opportunity = retracement * trend_strength
    
    return opportunity

def calculate_consolidation_breakout(close, high, low, volume, window=20):
    """盘整突破"""
    # 波动率收缩
    volatility = (high - low) / close
    vol_contraction = volatility / volatility.rolling(window=window).mean()
    
    # 成交量收缩
    volume_contraction = volume / volume.rolling(window=window).mean()
    
    # 盘整程度
    consolidation = (1 - vol_contraction) * (1 - volume_contraction)
    
    # 突破信号
    breakout = ((close > high.rolling(window=window).max().shift(1)) | 
                (close < low.rolling(window=window).min().shift(1))).astype(int)
    
    return consolidation.shift(1) * breakout

def calculate_overextension_risk(close, window=20):
    """过度延伸风险"""
    # 标准差倍数
    ma = close.rolling(window=window).mean()
    std = close.rolling(window=window).std()
    z_score = (close - ma) / (std + 1e-10)
    
    # 过度延伸风险
    overextension_risk = np.abs(z_score) / 2  # 标准化到合理范围
    
    return overextension_risk

def calculate_sustainable_momentum(close, volume, window=20):
    """可持续动量"""
    # 价格动量
    price_momentum = calculate_momentum(close, window)
    
    # 成交量支撑
    volume_ma = volume.rolling(window=window).mean()
    volume_support = np.minimum(volume / volume_ma, 2)  # 限制最大值
    
    # 波动率因子
    volatility = close.pct_change().rolling(window=window).std()
    volatility_factor = 1 / (1 + volatility * 10)  # 低波动率得分更高
    
    # 可持续动量
    sustainable = price_momentum * volume_support * volatility_factor
    
    return sustainable

def calculate_buying_climax(close, volume, high, low, window=10):
    """买入高潮"""
    price_acc = calculate_acceleration(close, window)
    
    # 成交量激增
    volume_spike = volume / volume.rolling(window=window*2).mean()
    
    # 上影线比例 (修正索引问题)
    upper_shadow = (high - close) / (high - low + 1e-10)  # 直接使用对齐的Series
    
    # 买入高潮指标
    climax = price_acc * volume_spike * (1 + upper_shadow)
    
    return climax

def calculate_entry_timing_score(close, volume, high, low, window=20):
    """入场时机评分"""
    # 价格位置得分（低位加分）
    price_position = 1 - calculate_high_low_position(high, low, close, window)
    
    # 成交量适中得分
    volume_ratio = volume / volume.rolling(window=window).mean()
    volume_score = 1 - np.abs(volume_ratio - 1)  # 接近平均成交量得分高
    
    # 波动率得分（低波动加分）
    volatility = close.pct_change().rolling(window=window).std()
    vol_percentile = volatility.rolling(window=window*3).rank(pct=True)
    volatility_score = 1 - vol_percentile
    
    # 综合入场时机评分
    timing_score = (price_position * 0.4 + 
                   volume_score * 0.3 + 
                   volatility_score * 0.3)
    
    return timing_score

def calculate_profit_taking_signal(close, high, window=20):
    """止盈信号"""
    # 获利水平
    profit_level = (close - close.rolling(window=window).min()) / \
                   close.rolling(window=window).min()
    
    # 从高点回撤
    drawdown = (high.rolling(window=5).max() - close) / \
               high.rolling(window=5).max()
    
    # 动量衰减
    momentum_decay = calculate_momentum(close, 5) / \
                     (calculate_momentum(close, 20) + 1e-10)
    
    # 止盈信号强度
    profit_taking = profit_level * drawdown * (2 - momentum_decay)
    
    return profit_taking

def calculate_stealth_accumulation(close, volume, low, high, window=20):
    """隐形吸筹"""
    # 价格稳定性
    price_stability = 1 / (close.pct_change().rolling(window=window).std() + 0.01)
    
    # 成交量稳定增长
    volume_trend = volume.rolling(window=5).mean() / \
                   volume.rolling(window=window).mean()
    
    # 低位盘整
    price_level = (close - low.rolling(window=window*2).min()) / \
                  (high.rolling(window=window*2).max() - low.rolling(window=window*2).min() + 1e-10)
    
    # 隐形吸筹指标
    stealth = price_stability * volume_trend * (1 - price_level)
    
    return stealth

def calculate_overreaction_reversal(close, volume, window=10):
    """过度反应反转"""
    # 短期剧烈变动
    short_change = close.pct_change(window)
    
    # 成交量异常
    volume_abnormal = volume / volume.rolling(window=window*2).median()
    
    # 过度反应程度
    overreaction = np.abs(short_change) * volume_abnormal
    
    # 反转概率（过度反应越大，反转概率越高）
    reversal_prob = 1 - np.exp(-overreaction)
    
    return reversal_prob * np.sign(-short_change)  # 反向信号

def calculate_fear_greed_indicator(close, volume, high, low, window=20):
    """恐惧贪婪指标"""
    # 价格动量
    momentum = calculate_momentum(close, window)
    
    # 波动率
    volatility = close.pct_change().rolling(window=window).std()
    
    # 成交量变化
    volume_change = volume / volume.rolling(window=window).mean() - 1
    
    # 市场宽度
    market_breadth = (close - low) / (high - low + 1e-10)
    
    # 恐惧贪婪综合指标
    # 正值表示贪婪，负值表示恐惧
    fear_greed = (momentum * 0.3 + 
                  (-volatility * 10) * 0.2 +  # 波动率越高越恐惧
                  volume_change * 0.2 + 
                  (market_breadth - 0.5) * 0.3)
    
    return fear_greed

def calculate_rally_sustainability(close, volume, window=20):
    """上涨持续性"""
    # 上涨天数比例
    up_days = (close > close.shift(1)).rolling(window=window).mean()
    
    # 成交量递增
    volume_trend = np.polyfit(range(window), 
                             volume.iloc[-window:].values, 1)[0] if len(volume) >= window else 0
    
    # 价格稳定上涨
    returns = close.pct_change()
    positive_returns = returns[returns > 0].rolling(window=window).mean()
    
    # 持续性指标
    sustainability = up_days * (1 + volume_trend) * positive_returns
    
    return pd.Series(sustainability, index=close.index).fillna(0)

def calculate_balanced_entry_zone(close, volume, high, low, window=20):
    """均衡入场区域"""
    # 价格均衡度
    price_balance = 1 - np.abs(calculate_high_low_position(high, low, close, window) - 0.5) * 2
    
    # 成交量均衡度
    volume_balance = 1 - np.abs(volume / volume.rolling(window=window).mean() - 1)
    
    # 波动率均衡度
    volatility = close.pct_change().rolling(window=window).std()
    vol_balance = 1 - volatility.rolling(window=window*2).rank(pct=True)
    
    # 均衡入场评分
    balanced_score = (price_balance * 0.4 + 
                     volume_balance * 0.3 + 
                     vol_balance * 0.3)
    
    return balanced_score

def calculate_chasing_avoidance_score(close, volume, high, window=10):
    """避免追高评分"""
    # 近期涨幅
    recent_gain = close.pct_change(window)
    
    # 距离高点
    distance_from_high = (high.rolling(window=window).max() - close) / close
    
    # 成交量异常度
    volume_unusual = np.abs(volume / volume.rolling(window=window*2).mean() - 1)
    
    # 避免追高评分（越高越应该避免）
    avoidance_score = (recent_gain * 0.4 + 
                      (-distance_from_high) * 0.3 +  # 越接近高点分数越高
                      volume_unusual * 0.3)
    
    return avoidance_score

def calculate_fakeout_detection(close, high, low, volume, window=20):
    """假突破检测"""
    # 突破后回落
    breakout_high = close > high.rolling(window=window).max().shift(1)
    pullback = close < close.shift(1)
    fake_breakout_up = (breakout_high.shift(1) & pullback).astype(int)
    
    # 跌破后反弹
    breakout_low = close < low.rolling(window=window).min().shift(1)
    bounce = close > close.shift(1)
    fake_breakout_down = (breakout_low.shift(1) & bounce).astype(int)
    
    # 成交量不足
    volume_insufficient = volume < volume.rolling(window=window).mean() * 0.8
    
    # 假突破综合检测
    fakeout = (fake_breakout_up - fake_breakout_down) * volume_insufficient.astype(int)
    
    return fakeout.rolling(window=5).sum()

def calculate_market_resilience(close, volume, low, window=20):
    """市场韧性"""
    # 下跌后快速恢复
    drawdown = (close - close.rolling(window=window).max()) / \
               close.rolling(window=window).max()
    recovery = close.pct_change(5)
    
    # 低位成交量保持
    low_position = (close - low.rolling(window=window).min()) / \
                   (close.rolling(window=window).max() - low.rolling(window=window).min() + 1e-10)
    volume_maintain = volume / volume.rolling(window=window).mean()
    
    # 韧性指标
    resilience = (-drawdown * recovery * volume_maintain) * (1 - low_position)
    
    return resilience

# --- 14. 风险管理因子 ---
def calculate_drawdown_control(close, window=20):
    """回撤控制"""
    # 滚动最大回撤
    rolling_max = close.rolling(window=window).max()
    drawdown = (close - rolling_max) / rolling_max
    
    # 回撤速度
    drawdown_speed = drawdown - drawdown.shift(5)
    
    # 回撤控制指标
    control = np.abs(drawdown) * (1 + np.abs(drawdown_speed))
    
    return control

def calculate_volatility_regime_control(close, window=20):
    """波动率体制控制"""
    returns = close.pct_change()
    
    # 当前波动率体制
    current_vol = returns.rolling(window=5).std()
    normal_vol = returns.rolling(window=window).std()
    
    # 波动率异常度
    vol_regime = current_vol / (normal_vol + 1e-10)
    
    # 控制信号
    control = np.where(vol_regime > 1.5, vol_regime, 1)  # 高波动时加强控制
    
    return control

def calculate_stop_loss_trigger(close, atr, window=20):
    """止损触发"""
    # 动态止损位
    rolling_high = close.rolling(window=window).max()
    stop_level = rolling_high - 2 * atr
    
    # 止损触发信号
    trigger = (close < stop_level).astype(int)
    
    # 止损紧急度
    urgency = (stop_level - close) / close
    
    return trigger * (1 + urgency)

def calculate_risk_reward_balance(close, atr, high, low, window=20):
    """风险收益平衡"""
    # 潜在收益
    resistance = high.rolling(window=window).max()
    potential_reward = (resistance - close) / close
    
    # 潜在风险
    support = low.rolling(window=window).min()
    potential_risk = (close - support) / close
    
    # 风险收益比
    risk_reward_ratio = potential_reward / (potential_risk + 1e-10)
    
    # 平衡指标
    balance = risk_reward_ratio / (1 + atr / close)  # ATR调整
    
    return balance

def calculate_trend_consistency_risk(close, window=20):
    """趋势一致性风险"""
    # 短中长期趋势
    trend_5 = np.sign(close - close.shift(5))
    trend_10 = np.sign(close - close.shift(10))
    trend_20 = np.sign(close - close.shift(20))
    
    # 趋势分歧度
    divergence = np.abs(trend_5 + trend_10 + trend_20) / 3
    
    # 风险指标（分歧越大风险越高）
    risk = 1 - divergence
    
    return risk.rolling(window=5).mean()

def calculate_risk_concentration_index(close, volume, window=20):
    """风险集中度指数"""
    # 价格集中度
    price_std = close.rolling(window=window).std()
    price_concentration = 1 / (price_std / close + 1e-10)
    
    # 成交量集中度
    volume_std = volume.rolling(window=window).std()
    volume_concentration = 1 / (volume_std / volume.rolling(window=window).mean() + 1e-10)
    
    # 综合集中度风险
    concentration_risk = (price_concentration + volume_concentration) / 2
    
    return concentration_risk

def calculate_tail_risk_factor(close, window=20):
    """尾部风险因子"""
    returns = close.pct_change()
    
    # 计算VaR (95%置信度)
    var_95 = returns.rolling(window=window).quantile(0.05)
    
    # 计算CVaR (条件风险价值)
    cvar = returns[returns <= var_95].rolling(window=window).mean()
    
    # 尾部风险
    tail_risk = np.abs(cvar) / returns.rolling(window=window).std()
    
    return tail_risk.fillna(0)

def calculate_downside_protection(close, low, window=20):
    """下行保护"""
    # 下行波动率
    returns = close.pct_change()
    downside_returns = returns[returns < 0]
    downside_vol = downside_returns.rolling(window=window).std()
    
    # 支撑强度
    support = low.rolling(window=window).min()
    support_strength = (close - support) / close
    
    # 下行保护指标
    protection = support_strength / (downside_vol + 0.01)
    
    return protection.fillna(1)

def calculate_max_adverse_excursion(close, window=20):
    """最大不利偏移"""
    # 计算每个位置的最大不利偏移
    mae = pd.Series(index=close.index, dtype=float)
    
    for i in range(window, len(close)):
        entry_price = close.iloc[i-window]
        min_price = close.iloc[i-window:i].min()
        mae.iloc[i] = (entry_price - min_price) / entry_price
    
    return mae

def calculate_moving_stop_loss(close, atr, multiplier=2):
    """移动止损"""
    # Chandelier Exit
    rolling_high = close.rolling(window=22).max()
    stop_long = rolling_high - multiplier * atr
    
    rolling_low = close.rolling(window=22).min()
    stop_short = rolling_low + multiplier * atr
    
    # 选择合适的止损位
    stop_loss = pd.Series(index=close.index, dtype=float)
    stop_loss[close > close.rolling(window=22).mean()] = stop_long[close > close.rolling(window=22).mean()]
    stop_loss[close <= close.rolling(window=22).mean()] = stop_short[close <= close.rolling(window=22).mean()]
    
    return stop_loss

def calculate_position_sizing_factor(close, atr, window=20):
    """仓位大小因子"""
    # 基于波动率的仓位调整
    returns = close.pct_change()
    volatility = returns.rolling(window=window).std()
    
    # 目标波动率
    target_vol = 0.02  # 2%目标波动率
    
    # 仓位大小因子
    position_size = target_vol / (volatility + 1e-10)
    position_size = position_size.clip(0.1, 2)  # 限制在10%-200%之间
    
    return position_size

def calculate_risk_oscillator(close, volume, high, low, window=14):
    """风险振荡器"""
    # 价格风险
    price_risk = calculate_volatility_regime(close, 5, 20)
    
    # 成交量风险
    volume_risk = volume.rolling(window=5).std() / volume.rolling(window=20).std()
    
    # 区间风险
    range_risk = (high - low) / close
    range_risk_ma = range_risk.rolling(window=window).mean()
    
    # 综合风险振荡器
    risk_osc = (price_risk * 0.4 + 
                volume_risk * 0.3 + 
                range_risk / range_risk_ma * 0.3)
    
    return risk_osc

def calculate_optimal_exit_timing(close, high, atr, window=20):
    """最优退出时机"""
    # 利润水平
    profit = (close - close.rolling(window=window).min()) / close.rolling(window=window).min()
    
    # 动量衰减
    momentum = calculate_momentum(close, 5)
    momentum_decay = momentum / momentum.rolling(window=10).max()
    if isinstance(atr, np.ndarray):
        atr = pd.Series(atr, index=close.index)
    # 波动率上升
    vol_increase = atr / atr.rolling(window=window).mean()
    
    # 退出时机评分
    exit_score = profit * (2 - momentum_decay) * vol_increase
    
    return exit_score

def calculate_volatility_based_stop(close, window=20, multiplier=2):
    """基于波动率的止损"""
    returns = close.pct_change()
    volatility = returns.rolling(window=window).std()
    
    # 动态止损距离
    stop_distance = close * volatility * multiplier
    
    # 止损价格
    stop_price = close - stop_distance
    
    return stop_price

def calculate_price_exhaustion(close, volume, rsi, window=20):
    """价格衰竭"""
    # 价格动量衰减
    momentum_5 = calculate_momentum(close, 5)
    momentum_20 = calculate_momentum(close, 20)
    momentum_exhaustion = momentum_5 / (momentum_20 + 1e-10)
    
    # 成交量衰减
    volume_ma_5 = volume.rolling(window=5).mean()
    volume_ma_20 = volume.rolling(window=20).mean()
    volume_exhaustion = volume_ma_5 / (volume_ma_20 + 1e-10)
    
    # RSI极值
    rsi_extreme = np.where(rsi > 70, (rsi - 70) / 30, 
                          np.where(rsi < 30, (30 - rsi) / 30, 0))
    
    # 综合衰竭指标
    exhaustion = (1 - momentum_exhaustion) * (1 - volume_exhaustion) * (1 + rsi_extreme)
    
    return exhaustion

def calculate_trend_reversal_risk(close, volume, high, low, window=20):
    """趋势反转风险"""
    # 趋势强度减弱
    trend_strength = calculate_trend_power(close, 
                                          calculate_ma(close, 5),
                                          calculate_ma(close, 10),
                                          calculate_ma(close, 20))
    trend_weakening = trend_strength / trend_strength.rolling(window=window).max()
    
    # 成交量背离
    price_trend = (close - close.shift(window)) / close.shift(window)
    volume_trend = (volume.rolling(window=5).mean() - volume.rolling(window=5).mean().shift(window)) / \
                   volume.rolling(window=5).mean().shift(window)
    divergence = np.abs(price_trend - volume_trend)
    
    # 极值位置
    position = calculate_high_low_position(high, low, close, window)
    extreme_position = np.abs(position - 0.5) * 2
    
    # 反转风险综合评分
    reversal_risk = (1 - trend_weakening) * divergence * extreme_position
    
    return reversal_risk

def calculate_price_momentum_divergence(close, window=14):
    """价格动量背离"""
    # 价格趋势
    price_trend = (close - close.shift(window)) / close.shift(window)
    
    # 动量指标
    momentum = calculate_momentum(close, window)
    momentum_trend = (momentum - momentum.shift(window)) / (np.abs(momentum.shift(window)) + 1e-10)
    
    # 背离程度
    divergence = price_trend - momentum_trend
    
    return divergence

def calculate_profit_protection_ratio(close, atr, high, window=20):
    """利润保护比率"""
    # 盈利水平
    rolling_low = close.rolling(window=window).min()
    profit_level = (close - rolling_low) / rolling_low
    if isinstance(atr, np.ndarray):
        atr = pd.Series(atr, index=close.index)
    # 波动率水平
    volatility_level = atr / close
    
    # 高点回撤
    rolling_high = high.rolling(window=window).max()
    drawdown_level = (rolling_high - close) / rolling_high
    
    # 保护比率计算
    protection_ratio = pd.Series(0.0, index=close.index)
    
    # 根据盈利水平设定保护级别
    protection_ratio[profit_level < 0.05] = 0.2
    protection_ratio[(profit_level >= 0.05) & (profit_level < 0.1)] = 0.4
    protection_ratio[(profit_level >= 0.1) & (profit_level < 0.2)] = 0.6
    protection_ratio[profit_level >= 0.2] = 0.8
    
    # 波动率和回撤调整
    vol_adjustment = np.minimum(1.0, volatility_level * 20)
    drawdown_adjustment = np.minimum(1.0, drawdown_level * 5)
    
    # 最终保护比率
    final_ratio = protection_ratio * 0.6 + vol_adjustment * 0.2 + drawdown_adjustment * 0.2
    
    return final_ratio

def calculate_parabolic_warning(close, window=20):
    """抛物线警告"""
    # 计算价格加速度
    velocity = close.pct_change()
    acceleration = velocity - velocity.shift(1)
    
    # 计算抛物线特征
    def parabolic_fit(x):
        if len(x) < 3:
            return 0
        y = np.array(x)
        x = np.arange(len(y))
        try:
            coeffs = np.polyfit(x, y, 2)
            return coeffs[0]  # 二次项系数
        except:
            return 0
    
    parabolic_coeff = close.rolling(window=window).apply(parabolic_fit)
    
    # 警告信号（系数越大越危险）
    warning = parabolic_coeff * acceleration.rolling(window=5).mean()
    
    return warning

def calculate_momentum_reversal_probability(close, volume, rsi, window=20):
    """动量反转概率"""
    # 极端动量
    momentum = calculate_momentum(close, window)
    momentum_extreme = np.abs(momentum) / momentum.rolling(window=window*2).std()
    
    # RSI极值
    rsi_extreme = np.where(rsi > 80, (rsi - 80) / 20, 
                          np.where(rsi < 20, (20 - rsi) / 20, 0))
    
    # 成交量异常
    volume_unusual = volume / volume.rolling(window=window).median()
    
    # 反转概率
    reversal_prob = 1 - np.exp(-(momentum_extreme * 0.4 + 
                                 rsi_extreme * 0.4 + 
                                 np.log(volume_unusual + 1) * 0.2))
    
    return reversal_prob

def calculate_risk_adjusted_momentum(close, atr, window=20):
    """风险调整动量"""
    # 基础动量
    momentum = calculate_momentum(close, window)
    
    if isinstance(atr, np.ndarray):
        atr = pd.Series(atr, index=close.index)
    # 风险调整
    risk = atr / close
    risk_factor = 1 / (1 + risk * 10)  # 风险越高，因子越小
    
    # 风险调整后的动量
    adjusted_momentum = momentum * risk_factor
    
    return adjusted_momentum

# --- 15. 复合策略因子 ---
def calculate_smart_trend_allocation(close, volume, atr, window=20):
    """智能趋势配置"""
    # 趋势强度
    trend_strength = calculate_trend_stability_index(close, window)
    if isinstance(atr, np.ndarray):
        atr = pd.Series(atr, index=close.index)
    # 风险水平
    risk_level = atr / close
    
    # 成交量支持
    volume_support = volume / volume.rolling(window=window).mean()
    
    # 智能配置得分
    allocation = trend_strength * (1 - risk_level * 5) * np.sqrt(volume_support)
    
    return allocation

def calculate_cross_asset_momentum(cb_close, stk_close, window=20):
    """跨资产动量"""
    # 可转债动量
    cb_momentum = calculate_momentum(cb_close, window)
    
    # 股票动量
    stk_momentum = calculate_momentum(stk_close, window)
    
    # 动量协同性
    momentum_sync = cb_momentum * stk_momentum / (np.abs(cb_momentum * stk_momentum) + 1e-10)
    
    # 跨资产动量
    cross_momentum = (cb_momentum + stk_momentum) / 2 * (1 + momentum_sync)
    
    return cross_momentum

def calculate_relative_value_factor(cb_close, cb_value, bond_value, stk_close):
    """相对价值因子"""
    # 转股价值偏离
    conversion_value = cb_value
    conversion_premium = (cb_close - conversion_value) / conversion_value
    
    # 纯债价值偏离
    bond_premium = (cb_close - bond_value) / bond_value
    
    # 股债相对强度
    cb_return = cb_close.pct_change(20)
    stk_return = stk_close.pct_change(20, fill_method=None)
    relative_strength = cb_return - stk_return
    
    # 相对价值综合因子
    relative_value = -conversion_premium * 0.5 - bond_premium * 0.3 + relative_strength * 0.2
    
    return relative_value

def calculate_optimal_conversion_timing(cb_close, cb_value, cb_over_rate, volume):
    """最优转股时机"""
    # 转股溢价率倒数
    premium_factor = 1 / (1 + np.abs(cb_over_rate) / 100)
    
    # 价值接近度
    value_proximity = 1 - np.abs(cb_close - cb_value) / cb_close
    
    # 流动性因子
    liquidity = volume / volume.rolling(window=20).mean()
    
    # 最优时机得分
    timing_score = premium_factor * value_proximity * np.sqrt(liquidity)
    
    return timing_score

def calculate_regime_adaptive_factor(close, volume, window=20):
    """体制自适应因子"""
    # 识别市场体制
    returns = close.pct_change()
    volatility = returns.rolling(window=window).std()
    
    # 高波动体制
    high_vol_regime = volatility > volatility.rolling(window=window*3).quantile(0.75)
    
    # 趋势体制
    trend_regime = np.abs(returns.rolling(window=window).mean()) > volatility
    
    # 震荡体制
    range_regime = (~high_vol_regime) & (~trend_regime)
    
    # 自适应因子
    adaptive_factor = pd.Series(index=close.index, dtype=float)
    
    # 高波动体制：保守
    adaptive_factor[high_vol_regime] = 0.5
    
    # 趋势体制：积极
    adaptive_factor[trend_regime] = 1.5
    
    # 震荡体制：中性
    adaptive_factor[range_regime] = 1.0
    
    return adaptive_factor.fillna(1.0)

def calculate_tactical_timing_factor(close, volume, rsi, bb_position, window=20):
    """战术时机因子"""
    # 超卖反弹时机
    oversold_bounce = ((rsi < 30) & (bb_position < 0.2) & 
                      (volume > volume.rolling(window=window).mean())).astype(int)
    
    # 突破追踪时机
    breakout_timing = ((close > close.rolling(window=window).max().shift(1)) & 
                      (volume > volume.rolling(window=window).mean() * 1.5)).astype(int)
    
    # 趋势延续时机
    trend_continuation = ((rsi > 50) & (rsi < 70) & 
                         (bb_position > 0.3) & (bb_position < 0.7)).astype(int)
    
    # 综合战术时机
    tactical_score = oversold_bounce * 2 + breakout_timing * 1.5 + trend_continuation * 1
    
    return tactical_score

def calculate_dynamic_hedging_factor(cb_close, stk_close, cb_over_rate, window=20):
    """动态对冲因子"""
    # 相关性
    correlation = cb_close.pct_change(fill_method=None).rolling(window=window).corr(stk_close.pct_change(fill_method=None))
    
    # 对冲比率
    cb_vol = cb_close.pct_change().rolling(window=window).std()
    stk_vol = stk_close.pct_change(fill_method=None).rolling(window=window).std()
    hedge_ratio = correlation * (cb_vol / stk_vol)
    
    # 对冲时机（溢价率高时对冲）
    hedge_timing = cb_over_rate / 100
    
    # 动态对冲因子
    hedging_factor = hedge_ratio * hedge_timing
    
    return hedging_factor

def calculate_triple_momentum_factor(close, volume, rsi, window=20):
    """三重动量因子"""
    # 价格动量
    price_momentum = calculate_momentum(close, window)
    
    # 成交量动量
    volume_momentum = calculate_momentum(volume, window)
    
    # RSI动量
    rsi_momentum = (rsi - 50) / 50
    
    # 三重动量
    triple_momentum = (price_momentum * 0.5 + 
                      volume_momentum * 0.3 + 
                      rsi_momentum * 0.2)
    
    # 动量一致性加成
    consistency = (np.sign(price_momentum) == np.sign(volume_momentum)).astype(int) * \
                 (np.sign(price_momentum) == np.sign(rsi_momentum)).astype(int)
    
    return triple_momentum * (1 + consistency * 0.5)

def calculate_optimal_entry_exit_factor(close, volume, atr, rsi, bb_position, high, low,window=20):
    """最优进出场因子"""
    # 入场条件
    entry_score = calculate_entry_timing_score(close, volume, high, low, window)
    if isinstance(atr, np.ndarray):
        atr = pd.Series(atr, index=close.index)
    # 出场条件
    exit_score = calculate_optimal_exit_timing(close, high, atr, window)
    
    # 持仓条件
    hold_score = ((rsi > 40) & (rsi < 70) & 
                  (bb_position > 0.2) & (bb_position < 0.8)).astype(float)
    
    # 综合因子
    optimal_factor = entry_score * (1 - exit_score) * hold_score
    
    return optimal_factor

def calculate_conversion_arbitrage_factor(cb_close, cb_value, cb_over_rate, volume):
    """转股套利因子"""
    # 套利空间
    arbitrage_space = -cb_over_rate / 100  # 负溢价率意味着套利机会
    
    # 流动性充足度
    liquidity_adequacy = np.minimum(volume / volume.rolling(window=20).mean(), 2)
    
    # 价值收敛速度
    value_convergence = -(cb_close - cb_value).diff() / cb_close
    
    # 套利因子
    arbitrage_factor = arbitrage_space * liquidity_adequacy * (1 + value_convergence)
    
    return arbitrage_factor

def calculate_composite_signal_quality(close, volume, rsi, macd_hist, adx, window=20):
    """复合信号质量"""
    # 技术指标一致性
    rsi_signal = np.where(rsi < 30, 1, np.where(rsi > 70, -1, 0))
    macd_signal = np.sign(macd_hist)
    trend_signal = np.where(adx > 25, 1, 0)
    
    # 信号强度
    signal_strength = np.abs(rsi_signal + macd_signal + trend_signal) / 3
    
    # 成交量确认
    volume_confirm = volume > volume.rolling(window=window).mean()
    
    # 信号质量
    signal_quality = signal_strength * volume_confirm.astype(int)
    
    return signal_quality

def calculate_reaction_speed_factor(close, volume, window=10):
    """反应速度因子"""
    # 价格反应速度
    price_change = close.pct_change()
    price_acceleration = price_change - price_change.shift(1)
    
    # 成交量反应速度
    volume_change = volume.pct_change()
    volume_acceleration = volume_change - volume_change.shift(1)
    
    # 反应速度综合
    reaction_speed = np.abs(price_acceleration) * (1 + np.abs(volume_acceleration))
    
    return reaction_speed.rolling(window=window).mean()

def calculate_probability_weighted_return(close, volume, rsi, bb_position, window=20):
    """概率加权收益"""
    # 计算各种情景的概率
    # 上涨概率
    up_prob = ((rsi < 70) & (bb_position < 0.8) & 
               (volume > volume.rolling(window=window).mean() * 0.8)).astype(float) * 0.6
    
    # 下跌概率
    down_prob = ((rsi > 70) | (bb_position > 0.9) | 
                 (volume < volume.rolling(window=window).mean() * 0.5)).astype(float) * 0.4
    
    # 预期收益
    historical_return = close.pct_change(window).mean()
    
    # 概率加权收益
    weighted_return = historical_return * (up_prob - down_prob)
    
    return weighted_return

def calculate_normalized_composite_momentum(close, volume, high, low, window=20):
    """标准化复合动量"""
    # 各种动量指标
    price_momentum = calculate_momentum(close, window)
    volume_momentum = calculate_momentum(volume, window)
    range_momentum = calculate_momentum(high - low, window)
    
    # 标准化
    def normalize(series):
        return (series - series.rolling(window=window*3).mean()) / \
               (series.rolling(window=window*3).std() + 1e-10)
    
    norm_price = normalize(price_momentum)
    norm_volume = normalize(volume_momentum)
    norm_range = normalize(range_momentum)
    
    # 复合动量
    composite = (norm_price * 0.5 + norm_volume * 0.3 + norm_range * 0.2)
    
    return composite

def calculate_extreme_regime_detector(close, volume, high, low, window=20):
    """极端体制检测器"""
    # 价格极端
    returns = close.pct_change()
    price_extreme = np.abs(returns) > returns.rolling(window=window*3).std() * 2.5
    
    # 成交量极端
    volume_extreme = volume > volume.rolling(window=window*3).quantile(0.95)
    
    # 波动率极端
    volatility = returns.rolling(window=window).std()
    vol_extreme = volatility > volatility.rolling(window=window*3).quantile(0.9)
    
    # 区间极端
    range_pct = (high - low) / close
    range_extreme = range_pct > range_pct.rolling(window=window*3).quantile(0.95)
    
    # 极端体制得分
    extreme_score = (price_extreme.astype(int) + 
                    volume_extreme.astype(int) + 
                    vol_extreme.astype(int) + 
                    range_extreme.astype(int))
    
    return extreme_score

def calculate_breakout_confirmation_index(close, volume, high, low, window=20):
    """突破确认指数"""
    # 价格突破
    resistance = high.rolling(window=window).max()
    support = low.rolling(window=window).min()
    
    breakout_up = close > resistance.shift(1)
    breakout_down = close < support.shift(1)
    
    # 成交量确认
    volume_surge = volume > volume.rolling(window=window).mean() * 1.5
    
    # 持续性确认
    continuation = pd.Series(0, index=close.index)
    for i in range(2, len(close)):
        if breakout_up.iloc[i-1]:
            continuation.iloc[i] = (close.iloc[i] > close.iloc[i-1]).astype(int)
        elif breakout_down.iloc[i-1]:
            continuation.iloc[i] = (close.iloc[i] < close.iloc[i-1]).astype(int)
    
    # 确认指数
    confirmation = ((breakout_up | breakout_down).astype(int) * 
                   volume_surge.astype(int) * 
                   continuation)
    
    return confirmation.rolling(window=3).sum()

def calculate_trend_efficiency_ratio(close, window=20):
    """趋势效率比率"""
    # 净变化
    net_change = np.abs(close - close.shift(window))
    
    # 总路径
    total_path = np.abs(close.diff()).rolling(window=window).sum()
    
    # 效率比率
    efficiency = net_change / (total_path + 1e-10)
    
    return efficiency

def calculate_price_velocity_factor(close, window=20):
    """价格速度因子"""
    # 一阶导数（速度）
    velocity = close.pct_change()
    
    # 平滑速度
    smooth_velocity = velocity.rolling(window=5).mean()
    
    # 速度的动量
    velocity_momentum = smooth_velocity - smooth_velocity.shift(window)
    
    return velocity_momentum

def calculate_stochastic_momentum_index(close, high, low, window=14):
    """随机动量指数"""
    # 计算中点
    midpoint = (high.rolling(window=window).max() + low.rolling(window=window).min()) / 2
    
    # 计算距离
    distance = close - midpoint
    
    # 计算范围
    range_hl = high.rolling(window=window).max() - low.rolling(window=window).min()
    
    # SMI
    smi = 100 * distance / (range_hl / 2 + 1e-10)
    
    # 平滑SMI
    smi_smooth = smi.rolling(window=3).mean()
    
    return smi_smooth


def calculate_contract_expansion_factor(high, low, close, window=20):
    """区间扩张因子"""
    # 真实区间
    true_range = pd.DataFrame({
        'hl': high - low,
        'hc': np.abs(high - close.shift(1)),
        'lc': np.abs(low - close.shift(1))
    }).max(axis=1)
    # 平均真实区间
    atr = true_range.rolling(window=window).mean()
    
    # 当前区间vs历史区间
    current_range = true_range
    expansion = current_range / (atr + 1e-10)
    
    # 扩张的持续性
    expansion_persistence = (expansion > 1.5).rolling(window=5).sum()
    
    return expansion_persistence

def calculate_volatility_squeeze_release(close, high, low, window=20):
    """波动率挤压释放"""
    # Bollinger Bands
    ma = close.rolling(window=window).mean()
    std = close.rolling(window=window).std()
    bb_upper = ma + 2 * std
    bb_lower = ma - 2 * std
    bb_width = bb_upper - bb_lower
    # Keltner Channels
    atr = calculate_atr(high, low, close, window)
    kc_upper = ma + 2 * atr
    kc_lower = ma - 2 * atr
    kc_width = kc_upper - kc_lower
    
    # 挤压状态
    squeeze = bb_width < kc_width
    
    # 挤压释放
    squeeze_release = (~squeeze) & squeeze.shift(1)
    
    # 释放方向
    release_direction = np.where(close > ma, 1, -1)
    
    return squeeze_release.astype(int) * release_direction



def calculate_open_high_return_5d(open_price, high):
    """
    计算收益率函数1：基于第二到第六天最高价的收益率
    计算 (第二到第六天最高价中的最大值/第二天开盘价 - 1) * 100
    
    参数:
    open_price: 开盘价序列
    high: 最高价序列
    
    返回:
    收益率百分比（真实数值）
    """
    # 第二天的开盘价
    next_day_open = open_price.shift(-1)
    
    # 获取第二到第六天的最高价
    high_prices = []
    for i in range(1, 6):  # 对应第二到第六天
        daily_high = high.shift(-i)
        high_prices.append(daily_high)
    
    # 找出第二到第六天最高价中的最大值
    max_high = high_prices[0]  # 初始化为第二天的最高价
    for price in high_prices[1:]:
        max_high = max_high.combine(price, max, fill_value=0)
    
    # 计算目标函数：(最大最高价/第二天开盘价 - 1) * 100
    target = (max_high / next_day_open - 1) * 100
    
    return target

def calculate_max_high_return_3_7d(close, high):
    """
    计算收益率函数：基于第三到第七天最高价的收益率
    计算 (第三到第七天最高价中的最大值 / 第二天收盘价 - 1) * 100
    
    参数:
    close: 收盘价序列
    high: 最高价序列
    
    返回:
    收益率百分比（真实数值）
    """
    # 第二天的收盘价
    day2_close = close.shift(-1)
    
    # 获取第三到第七天的最高价
    high_prices = []
    for i in range(2, 7):  # 对应第三到第七天（shift(-2)到shift(-6)）
        daily_high = high.shift(-i)
        high_prices.append(daily_high)
    
    # 找出第三到第七天最高价中的最大值
    max_high = high_prices[0]  # 初始化为第三天的最高价
    for price in high_prices[1:]:
        max_high = max_high.combine(price, max, fill_value=0)
    
    # 计算目标函数：(最大最高价 / 第二天收盘价 - 1) * 100
    target = (max_high / day2_close - 1) * 100
    
    return target

def calculate_day2_close_day3_high_return(open_price, close, high):
    """
    计算收益率函数：基于第二天收盘价和第三天最高价的收益率
    计算 (第二天收盘价和第三天最高价中的最大值 / 第二天开盘价 - 1) * 100
    
    参数:
    open_price: 开盘价序列
    close: 收盘价序列
    high: 最高价序列
    
    返回:
    收益率百分比（真实数值）
    """
    # 第二天的开盘价
    next_day_open = open_price.shift(-1)
    
    # 获取第二天的收盘价和第三天的最高价
    day2_close = close.shift(-1)
    day3_high = high.shift(-2)
    
    # 找出第二天收盘价和第三天最高价中的最大值
    max_value = day2_close.combine(day3_high, max, fill_value=0)
    
    # 计算目标函数：(最大值 / 第二天开盘价 - 1) * 100
    target = (max_value / next_day_open - 1) * 100
    
    return target


def calculate_day3_high_to_day2_close(close, high):
    """
    计算第三天的最高价与第二天收盘价的比值（不乘以100）
    计算 (第三天的最高价 / 第二天的收盘价)
    
    参数:
    close: 收盘价序列
    high: 最高价序列
    
    返回:
    比值（无单位，如 1.05 表示第三天最高价比第二天收盘价高5%）
    """
    # 第二天的收盘价
    day2_close = close.shift(-1)
    
    # 第三天的最高价
    day3_high = high.shift(-2)
    
    # 计算目标函数：第三天的最高价 / 第二天的收盘价
    ratio = (day3_high / day2_close-1)*100
    
    return ratio


# --- 主函数：计算所有因子 ---
def calculate_factors_for_bond(bond_data, current_date=None):
    """为单个ts_code的可转债数据计算所有因子"""
    ts_code = bond_data['ts_code'].iloc[0]
    
    # 确保数据按日期排序
    bond_data = bond_data.sort_values(by='trade_date')
    
    # 检查数据量是否足够进行计算
    if len(bond_data) < MIN_OBS_FOR_CALC:
        print(f"数据不足，跳过 {ts_code} (需要 {MIN_OBS_FOR_CALC} 天, 只有 {len(bond_data)} 天)")
        return None
    
    # 创建结果字典
    results_dict = {}
    
    # --- 1. 基础数据 ---
    results_dict['ts_code'] = bond_data['ts_code']
    results_dict['trade_date'] = bond_data['trade_date']
    results_dict['open'] = bond_data['open']
    results_dict['high'] = bond_data['high']
    results_dict['low'] = bond_data['low']
    results_dict['close'] = bond_data['close']
    results_dict['pct_chg'] = bond_data['pct_chg']
    results_dict['vol'] = bond_data['vol']
    results_dict['amount'] = bond_data['amount']
    results_dict['bond_value'] = bond_data['bond_value']
    results_dict['bond_over_rate'] = bond_data['bond_over_rate']
    results_dict['cb_value'] = bond_data['cb_value']
    results_dict['cb_over_rate'] = bond_data['cb_over_rate']
    
    # 提取常用价格序列
    close = bond_data['close'].astype(float)
    high = bond_data['high'].astype(float)
    low = bond_data['low'].astype(float)
    open_price = bond_data['open'].astype(float)
    volume = bond_data['vol'].astype(float)
    amount = bond_data['amount'].astype(float)
    pct_chg = bond_data['pct_chg'].astype(float)
    bond_value = bond_data['bond_value'].astype(float)
    bond_over_rate = bond_data['bond_over_rate'].astype(float)
    cb_value = bond_data['cb_value'].astype(float)
    cb_over_rate = bond_data['cb_over_rate'].astype(float)
    pre_close = bond_data['pre_close'].astype(float)
    
    # 股票数据
    stk_close = bond_data['stk_close'].astype(float)
    stk_pre_close = bond_data['stk_pre_close'].astype(float)
    stk_open = bond_data['stk_open'].astype(float)
    stk_high = bond_data['stk_high'].astype(float)
    stk_low = bond_data['stk_low'].astype(float)
    stk_vol = bond_data['stk_vol'].astype(float)
    stk_amount = bond_data['stk_amount'].astype(float)
    

    #目标函数（不是因子）
    
    results_dict['open_high_return_5d'] = calculate_open_high_return_5d(open_price, high)
    results_dict['max_high_return_3_7d'] = calculate_max_high_return_3_7d(close, high)
    results_dict['day2_close_day3_high'] = calculate_day2_close_day3_high_return(open_price, close, high)
    results_dict['day3_high_to_day2_close'] = calculate_day3_high_to_day2_close(close, high)

    # --- 2. 动量类因子 ---
    results_dict['cb_momentum_5d'] = calculate_momentum(close, 5)
    results_dict['cb_momentum_10d'] = calculate_momentum(close, 10)
    results_dict['cb_momentum_20d'] = calculate_momentum(close, 20)
    results_dict['cb_momentum_score'] = calculate_momentum_score(
        results_dict['cb_momentum_5d'], 
        results_dict['cb_momentum_10d'], 
        results_dict['cb_momentum_20d']
    )
    results_dict['cb_momentum_consistency'] = calculate_momentum_consistency(close)
    results_dict['cb_acceleration'] = calculate_acceleration(close)
    results_dict['cb_momentum_divergence'] = calculate_momentum_divergence(close, volume)
    results_dict['cb_low_vol_momentum'] = calculate_low_vol_momentum(close, volume)
    
    # --- 3. 波动率类因子 ---
    results_dict['cb_atr'] = calculate_atr(high, low, close, ATR_WINDOW, volume)
    results_dict['cb_natr'] = calculate_natr(high, low, close)
    results_dict['cb_volatility_decay'] = calculate_volatility_decay(close)
    results_dict['cb_volatility_regime'] = calculate_volatility_regime(close)
    results_dict['cb_range_expansion'] = calculate_range_expansion(high, low, close)
    
    # --- 4. 趋势类因子 ---
    results_dict['cb_ma_5d'] = calculate_ma(close, 5)
    results_dict['cb_ma_10d'] = calculate_ma(close, 10)
    results_dict['cb_ma_20d'] = calculate_ma(close, 20)
    results_dict['cb_bias_10d'] = calculate_bias(close, results_dict['cb_ma_10d'])
    results_dict['cb_bias_20d'] = calculate_bias(close, results_dict['cb_ma_20d'])
    results_dict['cb_trend_power'] = calculate_trend_power(
        close, 
        results_dict['cb_ma_5d'], 
        results_dict['cb_ma_10d'], 
        results_dict['cb_ma_20d']
    )
    results_dict['cb_early_trend_recognition'] = calculate_early_trend_recognition(close, volume)
    
    # --- 5. 技术指标类因子 ---
    results_dict['cb_rsi'] = calculate_rsi(close)
    results_dict['cb_rsi_divergence'] = calculate_rsi_divergence(close, results_dict['cb_rsi'])
    results_dict['cb_volume_weighted_rsi'] = calculate_volume_weighted_rsi(close, volume)
    
    k, d, j = calculate_kdj(high, low, close)
    results_dict['cb_kdj_k'] = k
    results_dict['cb_kdj_d'] = d
    results_dict['cb_kdj_j'] = j
    
    bb_position, bb_width = calculate_bb_indicators(close)
    results_dict['cb_bb_position'] = bb_position
    results_dict['cb_bb_width'] = bb_width
    results_dict['cb_price_efficiency'] = calculate_price_efficiency(close)
    
    macd_cross, macd_zero, macd_hist = calculate_macd_indicators(close)
    results_dict['cb_macd_cross_signal'] = macd_cross
    results_dict['cb_macd_zero_cross'] = macd_zero
    results_dict['cb_macd_hist'] = macd_hist
    
    adx, plus_di, minus_di, di_diff = calculate_adx_indicators(high, low, close)
    results_dict['cb_adx'] = adx
    results_dict['cb_plus_di'] = plus_di
    results_dict['cb_minus_di'] = minus_di
    results_dict['cb_di_diff'] = di_diff
    
    results_dict['cb_roc_10d'] = calculate_roc(close)
    
    # --- 6. 成交量类因子 ---
    obv, obv_trend = calculate_obv_indicators(close, volume)
    results_dict['cb_obv'] = obv
    results_dict['cb_obv_trend'] = obv_trend
    
    mfi, mfi_divergence = calculate_mfi_indicators(high, low, close, volume)
    results_dict['cb_mfi'] = mfi
    results_dict['cb_mfi_divergence'] = mfi_divergence
    
    results_dict['cb_smart_money'] = calculate_smart_money(close, volume, high, low)
    results_dict['cb_volume_profile'] = calculate_volume_profile(close, volume)
    results_dict['cb_vp_momentum_penalty'] = calculate_vp_momentum_penalty(close, volume)
    
    # --- 7. K线形态类因子 ---
    candle_5d = calculate_candle_patterns(open_price, high, low, close, 5)
    results_dict['cb_close_position_5d'] = candle_5d[0]
    results_dict['cb_upper_shadow_5d'] = candle_5d[1]
    results_dict['cb_lower_shadow_5d'] = candle_5d[2]
    results_dict['cb_body_ratio_5d'] = candle_5d[3]
    results_dict['cb_candle_color_ratio_5d'] = candle_5d[4]
    
    candle_10d = calculate_candle_patterns(open_price, high, low, close, 10)
    results_dict['cb_close_position_10d'] = candle_10d[0]
    results_dict['cb_upper_shadow_10d'] = candle_10d[1]
    results_dict['cb_lower_shadow_10d'] = candle_10d[2]
    results_dict['cb_body_ratio_10d'] = candle_10d[3]
    results_dict['cb_candle_color_ratio_10d'] = candle_10d[4]
    
    # --- 8. 市场微观结构类因子 ---
    results_dict['cb_vwap'] = calculate_vwap(close, volume)
    
    avg_cost, high_cost_ratio, profit_ratio, concentration = calculate_cost_distribution(
        close, volume, results_dict['cb_vwap']
    )
    results_dict['cb_avg_cost'] = avg_cost
    results_dict['cb_high_cost_ratio'] = high_cost_ratio
    results_dict['cb_profit_ratio'] = profit_ratio
    results_dict['cb_concentration'] = concentration
    
    results_dict['cb_fractal_indicator'] = calculate_fractal_indicator(high, low)
    results_dict['cb_swing_strength'] = calculate_swing_strength(high, low, close)
    results_dict['cb_high_low_position'] = calculate_high_low_position(high, low, close)
    
    top_risk, bottom_chance = calculate_reversal_indicators(high, low, close, volume)
    results_dict['cb_top_reversal_risk'] = top_risk
    results_dict['cb_bottom_reversal_chance'] = bottom_chance
    
    results_dict['cb_mean_reversion'] = calculate_mean_reversion(close)
    results_dict['cb_hidden_divergence'] = calculate_hidden_divergence(close, volume)
    results_dict['cb_failure_swing'] = calculate_failure_swing(close)
    
    # --- 9. 套利类因子 ---
    results_dict['cb_bond_equity_ratio'] = calculate_bond_equity_ratio(cb_value, bond_value)
    results_dict['cb_arbitrage_window'] = calculate_arbitrage_window(close, cb_value, cb_over_rate)
    results_dict['cb_extreme_factor'] = calculate_extreme_factor(close, volume, cb_over_rate)
    
    # --- 10. 股票联动因子 ---
    stk_momentums = calculate_stock_momentum(stk_close)
    results_dict['stk_momentum_5d'] = stk_momentums['stk_momentum_5d']
    results_dict['stk_momentum_10d'] = stk_momentums['stk_momentum_10d']
    results_dict['stk_momentum_20d'] = stk_momentums['stk_momentum_20d']
    results_dict['return_diff_5d'] = calculate_return_diff(close, stk_close)
    
    # --- 11. 高级策略因子 ---
    results_dict['cb_anti_chasing'] = calculate_anti_chasing(close, volume)
    results_dict['cb_key_level_breakthrough'] = calculate_key_level_breakthrough(close, high, low)
    results_dict['cb_pressure_release'] = calculate_pressure_release(close, volume, high)
    results_dict['cb_accumulation_signal'] = calculate_accumulation_signal(close, volume, low)
    results_dict['cb_pullback_opportunity'] = calculate_pullback_opportunity(close)
    results_dict['cb_continuation_pattern'] = calculate_continuation_pattern(close, volume)
    results_dict['cb_volume_price_confirmation'] = calculate_volume_price_confirmation(close, volume)
    results_dict['cb_momentum_quality'] = calculate_momentum_quality(close, volume, high, low)
    results_dict['cb_trend_confirmation_index'] = calculate_trend_confirmation_index(close, volume)
    results_dict['cb_volatility_adjusted_momentum'] = calculate_volatility_adjusted_momentum(close)
    results_dict['cb_chande_momentum_oscillator'] = calculate_chande_momentum_oscillator(close)
    results_dict['cb_advanced_breakout_score'] = calculate_advanced_breakout_score(close, volume, high, low)
    results_dict['cb_trend_stability_index'] = calculate_trend_stability_index(close)
    results_dict['cb_pattern_recognition_score'] = calculate_pattern_recognition_score(open_price, high, low, close)
    results_dict['cb_support_resistance_zones'] = calculate_support_resistance_zones(high, low, close)
    results_dict['cb_intraday_strength'] = calculate_intraday_strength(open_price, high, low, close)
    results_dict['cb_trend_regression_factor'] = calculate_trend_regression_factor(close)
    results_dict['cb_gap_analysis_factor'] = calculate_gap_analysis_factor(open_price, close, low, high)
    results_dict['cb_opening_range_breakout'] = calculate_opening_range_breakout(open_price, high, low, close)
    results_dict['cb_momentum_divergence_factor'] = calculate_momentum_divergence_factor(close, volume, high, low)
    results_dict['cb_volume_spike_reversal'] = calculate_volume_spike_reversal(close, volume)
    results_dict['cb_volume_price_trend'] = calculate_volume_price_trend(close, volume)
    
    # --- 12. 防追高因子组 ---
    results_dict['cb_anti_fomo_index'] = calculate_anti_fomo_index(close, volume)
    results_dict['cb_exhaustion_indicator'] = calculate_exhaustion_indicator(close, volume, high)
    results_dict['cb_overbought_oscillator'] = calculate_overbought_oscillator(close, volume)
    results_dict['cb_rush_index'] = calculate_rush_index(close, volume)
    results_dict['cb_price_thrust_control'] = calculate_price_thrust_control(close, high, low)
    results_dict['cb_retracement_opportunity'] = calculate_retracement_opportunity(close, high)
    results_dict['cb_consolidation_breakout'] = calculate_consolidation_breakout(close, high, low, volume)
    results_dict['cb_overextension_risk'] = calculate_overextension_risk(close)
    results_dict['cb_sustainable_momentum'] = calculate_sustainable_momentum(close, volume)
    results_dict['cb_buying_climax'] = calculate_buying_climax(close, volume, high, low)
    results_dict['cb_entry_timing_score'] = calculate_entry_timing_score(close, volume, high, low)
    results_dict['cb_profit_taking_signal'] = calculate_profit_taking_signal(close, high)
    results_dict['cb_stealth_accumulation'] = calculate_stealth_accumulation(close, volume, low ,high)
    results_dict['cb_overreaction_reversal'] = calculate_overreaction_reversal(close, volume)
    results_dict['cb_fear_greed_indicator'] = calculate_fear_greed_indicator(close, volume, high, low)
    results_dict['cb_rally_sustainability'] = calculate_rally_sustainability(close, volume)
    results_dict['cb_balanced_entry_zone'] = calculate_balanced_entry_zone(close, volume, high, low)
    results_dict['cb_chasing_avoidance_score'] = calculate_chasing_avoidance_score(close, volume, high)
    results_dict['cb_fakeout_detection'] = calculate_fakeout_detection(close, high, low, volume)
    results_dict['cb_market_resilience'] = calculate_market_resilience(close, volume, low)
    
    # --- 13. 风险管理因子 ---
    results_dict['cb_drawdown_control'] = calculate_drawdown_control(close)
    results_dict['cb_volatility_regime_control'] = calculate_volatility_regime_control(close)
    results_dict['cb_stop_loss_trigger'] = calculate_stop_loss_trigger(close, results_dict['cb_atr'])
    results_dict['cb_risk_reward_balance'] = calculate_risk_reward_balance(close, results_dict['cb_atr'], high, low)
    results_dict['cb_trend_consistency_risk'] = calculate_trend_consistency_risk(close)
    results_dict['cb_risk_concentration_index'] = calculate_risk_concentration_index(close, volume)
    results_dict['cb_tail_risk_factor'] = calculate_tail_risk_factor(close)
    results_dict['cb_downside_protection'] = calculate_downside_protection(close, low)
    results_dict['cb_max_adverse_excursion'] = calculate_max_adverse_excursion(close)
    results_dict['cb_moving_stop_loss'] = calculate_moving_stop_loss(close, results_dict['cb_atr'])
    results_dict['cb_position_sizing_factor'] = calculate_position_sizing_factor(close, results_dict['cb_atr'])
    results_dict['cb_risk_oscillator'] = calculate_risk_oscillator(close, volume, high, low)
    results_dict['cb_optimal_exit_timing'] = calculate_optimal_exit_timing(close, high, results_dict['cb_atr'])
    results_dict['cb_volatility_based_stop'] = calculate_volatility_based_stop(close)
    results_dict['cb_price_exhaustion'] = calculate_price_exhaustion(close, volume, results_dict['cb_rsi'])
    results_dict['cb_trend_reversal_risk'] = calculate_trend_reversal_risk(close, volume, high, low)
    results_dict['cb_price_momentum_divergence'] = calculate_price_momentum_divergence(close)
    results_dict['cb_profit_protection_ratio'] = calculate_profit_protection_ratio(close, results_dict['cb_atr'], high)
    results_dict['cb_parabolic_warning'] = calculate_parabolic_warning(close)
    results_dict['cb_momentum_reversal_probability'] = calculate_momentum_reversal_probability(close, volume, results_dict['cb_rsi'])
    results_dict['cb_risk_adjusted_momentum'] = calculate_risk_adjusted_momentum(close, results_dict['cb_atr'])
    
    # --- 14. 复合策略因子 ---
    results_dict['cb_smart_trend_allocation'] = calculate_smart_trend_allocation(close, volume, results_dict['cb_atr'])
    results_dict['cb_cross_asset_momentum'] = calculate_cross_asset_momentum(close, stk_close)
    results_dict['cb_relative_value_factor'] = calculate_relative_value_factor(close, cb_value, bond_value, stk_close)
    results_dict['cb_optimal_conversion_timing'] = calculate_optimal_conversion_timing(close, cb_value, cb_over_rate, volume)
    results_dict['cb_regime_adaptive_factor'] = calculate_regime_adaptive_factor(close, volume)
    results_dict['cb_tactical_timing_factor'] = calculate_tactical_timing_factor(close, volume, results_dict['cb_rsi'], results_dict['cb_bb_position'])
    results_dict['cb_dynamic_hedging_factor'] = calculate_dynamic_hedging_factor(close, stk_close, cb_over_rate)
    results_dict['cb_triple_momentum_factor'] = calculate_triple_momentum_factor(close, volume, results_dict['cb_rsi'])
    results_dict['cb_optimal_entry_exit_factor'] = calculate_optimal_entry_exit_factor(
        close, volume, results_dict['cb_atr'], results_dict['cb_rsi'], results_dict['cb_bb_position'], high, low
    )
    results_dict['cb_conversion_arbitrage_factor'] = calculate_conversion_arbitrage_factor(close, cb_value, cb_over_rate, volume)
    results_dict['cb_composite_signal_quality'] = calculate_composite_signal_quality(
        close, volume, results_dict['cb_rsi'], results_dict['cb_macd_hist'], results_dict['cb_adx']
    )
    results_dict['cb_reaction_speed_factor'] = calculate_reaction_speed_factor(close, volume)
    results_dict['cb_probability_weighted_return'] = calculate_probability_weighted_return(
        close, volume, results_dict['cb_rsi'], results_dict['cb_bb_position']
    )
    results_dict['cb_normalized_composite_momentum'] = calculate_normalized_composite_momentum(close, volume, high, low)
    results_dict['cb_extreme_regime_detector'] = calculate_extreme_regime_detector(close, volume, high, low)
    results_dict['cb_breakout_confirmation_index'] = calculate_breakout_confirmation_index(close, volume, high, low)
    results_dict['cb_trend_efficiency_ratio'] = calculate_trend_efficiency_ratio(close)
    results_dict['cb_price_velocity_factor'] = calculate_price_velocity_factor(close)
    results_dict['cb_stochastic_momentum_index'] = calculate_stochastic_momentum_index(close, high, low)

    results_dict['cb_contract_expansion_factor'] = calculate_contract_expansion_factor(high, low, close)
    results_dict['cb_volatility_squeeze_release'] = calculate_volatility_squeeze_release(close, high, low)
    
    # --- 创建最终的DataFrame ---
    results_df = pd.DataFrame(results_dict, index=bond_data.index)
    results_df = results_df.replace([np.inf, -np.inf, np.nan], 0)
    
    # 使用MIN_OBS_FOR_CALC作为起始点，同时排除最后五个交易日，仅返回有足够历史数据且无未来数据缺失的行
    if len(results_df) > MIN_OBS_FOR_CALC + 5:  # 确保有足够的行可以返回（MIN_OBS_FOR_CALC + 5行）
        # 返回从MIN_OBS_FOR_CALC-1开始，到倒数第6行的数据（排除最后5行）
        return results_df.iloc[MIN_OBS_FOR_CALC-1:-5]
    elif len(results_df) > MIN_OBS_FOR_CALC:  # 如果数据不够多，但至少有MIN_OBS_FOR_CALC行
        print(f"警告: {ts_code} 数据行数不足以排除最后五个交易日 ({len(results_df)} 行)")
        # 尽可能多地排除数据，至少保留MIN_OBS_FOR_CALC行
        exclude_count = min(5, len(results_df) - MIN_OBS_FOR_CALC)
        return results_df.iloc[MIN_OBS_FOR_CALC-1:-exclude_count] if exclude_count > 0 else results_df.iloc[MIN_OBS_FOR_CALC-1:]
    else:
        return None  # 数据不足以进行计算

# --- 5. 主程序 ---
if __name__ == '__main__':
    print("开始执行可转债因子计算...")
    start_time = datetime.datetime.now()

    # --- 加载和预处理数据 ---
    print("加载并预处理数据...")
    cb_basic, cb_daily, stock_daily, cb_merged = load_and_preprocess_data(
        CB_BASIC_FILE, CB_DAILY_FILE, STOCK_DAILY_FILE
    )

    if cb_merged is None:
        print("数据加载或预处理失败，程序终止。")
        exit()

    print(f"原始数据加载完成。可转债行情数据 {len(cb_daily)} 条，合并后数据 {len(cb_merged)} 条。")

    # --- 增量更新处理 ---
    existing_data = None
    last_dates = {}
    if os.path.exists(OUTPUT_FILE):
        print(f"检测到已存在的结果文件: {OUTPUT_FILE}，将进行增量更新。")
        try:
            existing_data = pd.read_csv(OUTPUT_FILE, encoding='utf-8', parse_dates=['trade_date'])
            # 找到每个ts_code已计算的最新日期
            if not existing_data.empty:
                last_dates = existing_data.groupby('ts_code')['trade_date'].max().to_dict()
                print(f"已加载 {len(existing_data)} 条历史计算结果。")
            else:
                print("历史结果文件为空。")
        except Exception as e:
            print(f"加载历史结果文件失败: {e}。将重新计算所有数据。")
            existing_data = None # 出错则重新计算
            last_dates = {}
    else:
        print("未找到历史结果文件，将计算所有数据。")

    # 过滤需要计算的数据
    if last_dates:
        unique_ts_codes_in_merged = cb_merged['ts_code'].unique()
        codes_to_process = []
        for code in unique_ts_codes_in_merged:
            last_calculated_date = last_dates.get(code)
            max_date_for_code = cb_merged[cb_merged['ts_code'] == code]['trade_date'].max()
            if last_calculated_date is None or max_date_for_code > last_calculated_date:
                 codes_to_process.append(code)

        if not codes_to_process:
            print("没有新的数据需要计算。")
            exit()

        print(f"需要处理或更新 {len(codes_to_process)} 个可转债的数据。")
        data_to_process = cb_merged[cb_merged['ts_code'].isin(codes_to_process)]

    else:
        # 如果没有历史数据，处理所有数据
        data_to_process = cb_merged
        codes_to_process = data_to_process['ts_code'].unique()
        print("将计算所有可转债的数据。")

    if data_to_process.empty:
         print("筛选后没有数据需要处理。")
         exit()

    # --- 并行计算 ---
    print(f"开始并行计算因子 (使用 {N_JOBS} 个进程)...")
    grouped_data = [group for name, group in data_to_process.groupby('ts_code')]

    # 使用 joblib 进行并行计算
    results_list = Parallel(n_jobs=N_JOBS, verbose=10)(
        delayed(calculate_factors_for_bond)(bond_group) for bond_group in grouped_data
    )

    # --- 合并结果 ---
    print("合并计算结果...")
    valid_results = [res for res in results_list if res is not None and not res.empty]

    if not valid_results:
        print("所有可转债计算均失败或无有效结果。")
        new_results_df = pd.DataFrame() # 创建空的DataFrame
    else:
        new_results_df = pd.concat(valid_results, ignore_index=True)
        new_results_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
        print(f"计算完成，得到 {len(new_results_df)} 条新因子数据。")

        # --- 筛选出真正"新"的数据行 ---
        if last_dates:
            rows_to_keep = []
            for code, group in new_results_df.groupby('ts_code'):
                last_calculated_date = last_dates.get(code)
                if last_calculated_date:
                    rows_to_keep.append(group[group['trade_date'] > last_calculated_date])
                else:
                    rows_to_keep.append(group)

            if rows_to_keep:
                 new_results_df_filtered = pd.concat(rows_to_keep, ignore_index=True)
                 print(f"筛选出 {len(new_results_df_filtered)} 条需要添加到结果文件的因子数据。")
            else:
                 new_results_df_filtered = pd.DataFrame() # 可能没有更新的数据
                 print("没有需要添加到结果文件的新日期数据。")
            new_results_df = new_results_df_filtered # 使用筛选后的结果

    # --- 合并新旧数据并保存 ---
    if existing_data is not None and not new_results_df.empty:
        print("合并新计算结果与历史结果...")
        # 获取共同列和所有列
        common_cols = existing_data.columns.intersection(new_results_df.columns).tolist()
        all_cols = existing_data.columns.union(new_results_df.columns).tolist()

        # 重新索引
        existing_data_reindexed = existing_data.reindex(columns=all_cols)
        new_results_df_reindexed = new_results_df.reindex(columns=all_cols)
        # 合并并去重
        final_df = pd.concat([existing_data_reindexed, new_results_df_reindexed], ignore_index=True)
        final_df = final_df.drop_duplicates(subset=['ts_code', 'trade_date'], keep='last') 
        final_df = final_df.sort_values(by=['ts_code', 'trade_date'])
    elif not new_results_df.empty:
        print("保存首次计算的结果...")
        final_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
    elif existing_data is not None:
         print("没有新数据需要更新，保留原始结果文件。")
         final_df = existing_data # 保持原样
    else:
         print("没有计算出任何结果，无法保存文件。")
         final_df = pd.DataFrame() # 创建空的DataFrame避免保存时出错

    # 保存最终结果到CSV
    if not final_df.empty:
        try:
            final_df.to_csv(OUTPUT_FILE, index=False, encoding='utf-8-sig') # 使用utf-8-sig确保Excel能正确打开中文
            print(f"结果已成功保存到: {OUTPUT_FILE}")
            print(f"最终文件包含 {len(final_df)} 条记录。")
        except Exception as e:
            print(f"保存结果文件失败: {e}")
    else:
        print("最终结果为空，未生成或更新文件。")

    end_time = datetime.datetime.now()
    print(f"总耗时: {end_time - start_time}")
