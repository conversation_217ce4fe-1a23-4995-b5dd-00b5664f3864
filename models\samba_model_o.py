import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
import sys
import os

# 导入配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

class RMSNorm(nn.Module):
    """
    RMS归一化层
    """
    def __init__(self, dim, eps=1e-5):
        """
        初始化RMS归一化层
        
        参数:
            dim: 输入特征维度
            eps: 数值稳定性常数
        """
        super().__init__()
        self.eps = eps
        self.weight = nn.Parameter(torch.ones(dim))

    def forward(self, x):
        """
        前向传播
        
        参数:
            x: 输入张量 [批大小, 序列长度, 隐藏维度]
            
        返回:
            归一化后的张量
        """
        # 计算RMS
        rms = torch.sqrt(torch.mean(x**2, dim=-1, keepdim=True) + self.eps)
        # 归一化并加权
        return self.weight * x / rms

class FeatureAttention(nn.Module):
    """特征注意力模块，用于动态选择重要特征"""
    def __init__(self, input_dim):
        super().__init__()
        self.attention = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.Linear(input_dim // 2, input_dim),
            nn.Sigmoid()
        )
        self.gate = nn.Parameter(torch.tensor(0.5))
        
    def forward(self, x):
        # x: [batch_size, seq_len, feature_dim]
        batch_size, seq_len, feature_dim = x.shape
        
        # 计算序列级别的特征重要性
        seq_feats = x.mean(dim=1)  # [batch_size, feature_dim]
        weights = self.attention(seq_feats)  # [batch_size, feature_dim]
        
        # 应用门控注意力权重
        gated_weights = self.gate * weights + (1 - self.gate)  # 保留部分原始信息
        
        # 应用注意力权重到所有时间步
        return x * gated_weights.unsqueeze(1)  # [batch_size, seq_len, feature_dim]

class FactorInteractionBlock(nn.Module):
    """建模所有时间步的因子间交互关系"""
    def __init__(self, input_dim, hidden_dim):
        super().__init__()
        self.factor_proj = nn.Linear(input_dim, hidden_dim)
        
        # 时间步注意力
        self.time_attention = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.Tanh(),
            nn.Linear(hidden_dim, 1)
        )
        
        # 因子交互模块
        self.interaction = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )
        
        self.norm = RMSNorm(hidden_dim)
        self.output_proj = nn.Linear(hidden_dim, 1)
        
    def forward(self, x):
        # x: [batch_size, seq_len, feature_dim]
        batch_size, seq_len, _ = x.shape
        
        # 投影到隐藏空间
        proj = self.factor_proj(x)  # [batch_size, seq_len, hidden_dim]
        
        # 对每个时间步进行因子交互建模
        interactions = []
        for t in range(seq_len):
            step_features = proj[:, t, :]  # [batch_size, hidden_dim]
            step_interaction = self.interaction(step_features)  # [batch_size, hidden_dim]
            enhanced = self.norm(step_features + step_interaction)  # [batch_size, hidden_dim]
            interactions.append(enhanced)
        
        # 堆叠所有时间步的结果
        all_interactions = torch.stack(interactions, dim=1)  # [batch_size, seq_len, hidden_dim]
        
        # 计算时间步注意力权重
        attn_scores = self.time_attention(all_interactions)  # [batch_size, seq_len, 1]
        attn_weights = F.softmax(attn_scores, dim=1)  # [batch_size, seq_len, 1]
        
        # 加权融合
        context = torch.sum(all_interactions * attn_weights, dim=1)  # [batch_size, hidden_dim]
        
        # 输出投影
        out = self.output_proj(context)  # [batch_size, 1]
        
        return out, context

class TimeFactorCrossAttention(nn.Module):
    """时间-因子交叉注意力模块"""
    def __init__(self, hidden_dim, num_heads=4, dropout=0.1):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        assert hidden_dim % num_heads == 0, "hidden_dim必须能被num_heads整除"
        
        # 多头注意力参数
        self.q_proj = nn.Linear(hidden_dim, hidden_dim)
        self.k_proj = nn.Linear(hidden_dim, hidden_dim)
        self.v_proj = nn.Linear(hidden_dim, hidden_dim)
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)
        
        self.dropout = nn.Dropout(dropout)
        self.norm = RMSNorm(hidden_dim)
        
    def forward(self, x):
        # x: [batch_size, seq_len, feature_dim, hidden_dim]
        batch_size, seq_len, feature_dim, _ = x.shape
        
        # 转换形状以处理时间和因子维度
        x_flat = x.reshape(batch_size, seq_len * feature_dim, self.hidden_dim)
        
        # 计算Q、K、V
        q = self.q_proj(x_flat).reshape(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x_flat).reshape(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x_flat).reshape(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力
        attn = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)
        attn = F.softmax(attn, dim=-1)
        attn = self.dropout(attn)
        
        # 应用注意力
        out = torch.matmul(attn, v)
        out = out.transpose(1, 2).reshape(batch_size, seq_len * feature_dim, self.hidden_dim)
        out = self.out_proj(out)
        
        # 残差连接和归一化
        out = self.norm(x_flat + out)
        
        # 恢复原始形状
        out = out.reshape(batch_size, seq_len, feature_dim, self.hidden_dim)
        
        return out

class MultiHeadAttention(nn.Module):
    """多头自注意力机制"""
    def __init__(self, d_model, num_heads, dropout=0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
        
    def forward(self, x, mask=None):
        batch_size, seq_len, d_model = x.shape
        
        # 计算Q、K、V
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        
        # 应用掩码（如果提供）
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 应用softmax
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力权重
        out = torch.matmul(attn_weights, v)
        out = out.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)
        
        # 输出投影
        out = self.out_proj(out)
        
        return out

class FeedForward(nn.Module):
    """前馈神经网络"""
    def __init__(self, d_model, d_ff, dropout=0.1):
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        return self.linear2(self.dropout(F.gelu(self.linear1(x))))

class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, max_seq_length=1000):
        super().__init__()
        
        pe = torch.zeros(max_seq_length, d_model)
        position = torch.arange(0, max_seq_length, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))
        
    def forward(self, x):
        return x + self.pe[:, :x.size(1)]

class TransformerBlock(nn.Module):
    """
    Transformer块，替代原来的双向Mamba块
    """
    def __init__(
        self, 
        d_model, 
        num_heads=8,
        d_ff=None,
        dropout=0.1
    ):
        super().__init__()
        if d_ff is None:
            d_ff = d_model * 4
            
        self.norm1 = RMSNorm(d_model)
        self.attention = MultiHeadAttention(d_model, num_heads, dropout)
        
        self.norm2 = RMSNorm(d_model)
        self.ffn = FeedForward(d_model, d_ff, dropout)
        
        self.dropout = nn.Dropout(dropout)

    def forward(self, x, mask=None):
        # 自注意力 + 残差连接
        attn_out = self.attention(self.norm1(x), mask)
        x = x + self.dropout(attn_out)
        
        # 前馈网络 + 残差连接
        ffn_out = self.ffn(self.norm2(x))
        x = x + self.dropout(ffn_out)
        
        return x

class AdaptiveGraphConvolutionalBlock(nn.Module):
    """
    增强版自适应图卷积块，添加了额外的非线性和归一化层
    """
    def __init__(
        self, 
        d_model, 
        n_features, 
        node_embedding_dim=16, 
        gnn_polynomial_order=5,
        dropout=0.1
    ):
        super().__init__()
        self.d_model = d_model
        self.n_features = n_features
        self.node_embedding_dim = node_embedding_dim
        self.K = gnn_polynomial_order
        
        # 节点嵌入矩阵
        self.node_embeddings = nn.Parameter(torch.randn(n_features, node_embedding_dim))
        
        # 高斯核缩放因子
        self.psi = nn.Parameter(torch.tensor(1.0))
        
        # 过滤器权重生成器
        self.F_w = nn.Parameter(torch.randn(node_embedding_dim, gnn_polynomial_order + 1, d_model))
        self.f_b = nn.Parameter(torch.zeros(node_embedding_dim))
        
        # 输出投影 - 使用两层MLP
        self.output_projection = nn.Sequential(
            nn.Linear(n_features, n_features // 2),
            nn.LayerNorm(n_features // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(n_features // 2, 1)
        )
        
        # 添加边缘注意力
        self.edge_attention = nn.Parameter(torch.ones(n_features, n_features))
        self.attention_temp = nn.Parameter(torch.tensor(1.0))

    def _compute_chebyshev_polynomials(self, A_norm, K):
        # 初始化切比雪夫多项式列表
        polynomials = [None] * (K + 1)
        
        # T_0(x) = I
        polynomials[0] = torch.eye(A_norm.size(0), device=A_norm.device)
        
        # T_1(x) = x
        polynomials[1] = A_norm
        
        # 递归计算高阶多项式
        for k in range(2, K + 1):
            # T_k(x) = 2x·T_(x) - T_(x)
            polynomials[k] = 2 * torch.matmul(A_norm, polynomials[k-1]) - polynomials[k-2]
            
        return polynomials

    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        
        # 取序列的最后一个时间步作为图卷积的输入
        x_last = x[:, -1, :]  # [批大小, 特征数]
        
        # 计算节点间距离矩阵
        node_sim = torch.matmul(self.node_embeddings, self.node_embeddings.t())  # [n_features, n_features]
        diag = torch.diag(node_sim)  # [n_features]
        
        # 采用高斯核计算邻接矩阵
        D = diag.unsqueeze(1) + diag.unsqueeze(0) - 2 * node_sim  # [n_features, n_features]
        A = torch.exp(-self.psi * D)  # [n_features, n_features]
        
        # 应用边缘注意力
        A = A * F.softmax(self.edge_attention * self.attention_temp, dim=1)
        
        # 归一化邻接矩阵（按行softmax）
        A_norm = F.softmax(A, dim=1)  # [n_features, n_features]
        
        # 计算切比雪夫多项式
        polynomials = self._compute_chebyshev_polynomials(A_norm, self.K)  # K+1个 [n_features, n_features] 矩阵
        
        # 根据节点嵌入生成过滤器权重和偏置
        W_filter = torch.matmul(self.node_embeddings, self.F_w.reshape(self.node_embedding_dim, -1)).reshape(
            self.n_features, self.K + 1, self.d_model)  # [n_features, K+1, d_model]
        b_filter = torch.matmul(self.node_embeddings, self.f_b)  # [n_features]
        
        # 应用图卷积
        out = torch.zeros(batch_size, self.n_features, device=x.device)
        
        for k in range(self.K + 1):
            # 应用多项式
            poly_x = torch.matmul(x_last, polynomials[k])  # [batch_size, n_features]
            
            # 应用特征特定的权重
            out += torch.sum(poly_x.unsqueeze(-1) * W_filter[:, k, :], dim=-1)  # [batch_size, n_features]
        
        # 添加偏置
        out += b_filter  # [batch_size, n_features]
        
        # 最后的线性投影得到标量输出
        out = self.output_projection(out)  # [batch_size, 1]
        
        return out.squeeze(-1)  # [batch_size]

class SAMBAModel(nn.Module):
    """整合全时间步因子交互的增强版SAMBA模型（使用Transformer替代Mamba）"""
    def __init__(
        self,
        input_dim,
        d_model=config.D_MODEL,
        n_layer=config.N_LAYER,
        num_heads=config.NUM_HEADS,
        gnn_k=config.GNN_K,
        node_embedding_dim=config.NODE_EMBEDDING_DIM,
        dropout=config.DROPOUT
    ):
        super().__init__()
        
        self.feature_attention = FeatureAttention(input_dim)
        self.input_projection = nn.Linear(input_dim, d_model)
        self.input_dropout = nn.Dropout(dropout)
        self.input_norm = nn.LayerNorm(d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model)
        
        # Transformer块替代Mamba块
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(
                d_model=d_model,
                num_heads=num_heads,
                d_ff=d_model * 4,
                dropout=dropout
            ) for _ in range(n_layer)
        ])
        
        self.layer_norms = nn.ModuleList([
            RMSNorm(d_model) for _ in range(n_layer)
        ])
        
        # 修改因子交互模块
        self.factor_interaction = FactorInteractionBlock(input_dim, d_model)
        
        # 因子嵌入层
        self.factor_embedding = nn.Linear(1, d_model)
        
        # 时间-因子交叉注意力
        self.time_factor_attention = TimeFactorCrossAttention(d_model, num_heads=4, dropout=dropout)
        
        # 自适应图卷积块
        self.agc_block = AdaptiveGraphConvolutionalBlock(
            d_model=d_model,
            n_features=input_dim,
            node_embedding_dim=node_embedding_dim,
            gnn_polynomial_order=gnn_k,
            dropout=dropout
        )
        
        # 输出组合层
        self.output_weight = nn.Parameter(torch.tensor([0.6, 0.4]))
        self.output_norm = nn.LayerNorm(1)
        
    def forward(self, x):
        # x: [批大小, 序列长度, 特征数]
        batch_size, seq_len, feature_dim = x.shape
        
        # 应用特征注意力
        x_attended = self.feature_attention(x)
        
        # 输入投影
        x_proj = self.input_projection(x_attended)
        x_proj = self.input_dropout(x_proj)
        x_proj = self.input_norm(x_proj)
        
        # 添加位置编码
        x_proj = self.pos_encoding(x_proj)
        
        # 保存初始残差
        residual = x_proj
        
        # 应用Transformer块
        for i, block in enumerate(self.transformer_blocks):
            x_proj = block(x_proj)
            if i % 2 == 1:
                x_proj = self.layer_norms[i](x_proj + residual)
                residual = x_proj
        
        # 因子间时序交互建模
        # 首先处理每个时间步和特征
        factor_features = []
        for t in range(seq_len):
            for f in range(feature_dim):
                # 取单个特征值并嵌入
                feature_val = x_attended[:, t, f:f+1]  # [batch_size, 1]
                embedded = self.factor_embedding(feature_val)  # [batch_size, d_model]
                factor_features.append(embedded)
        
        # 重塑为 [batch_size, seq_len, feature_dim, d_model]
        factor_features = torch.stack(factor_features, dim=1)
        factor_features = factor_features.reshape(batch_size, seq_len, feature_dim, -1)
        
        # 应用时间-因子交叉注意力
        factor_context = self.time_factor_attention(factor_features)
        
        # 压缩时间和特征维度，得到上下文表示
        factor_context = factor_context.mean(dim=2).mean(dim=1)  # [batch_size, d_model]
        
        # 应用修改后的因子交互模块处理全时间步数据
        factor_out, factor_feats = self.factor_interaction(x_attended)
        
        # 应用自适应图卷积块
        agc_out = self.agc_block(x_attended)
        
        # 计算加权组合输出
        weights = F.softmax(self.output_weight, dim=0)
        combined_out = weights[0] * agc_out + weights[1] * factor_out.squeeze(-1)
        
        return combined_out
