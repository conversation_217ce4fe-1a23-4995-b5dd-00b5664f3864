{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b9c08c27", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_8540\\296679825.py:17: DtypeWarning: Columns (122) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv(csv_file)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "文件 'cb_factors2.csv' 中列 'open' 的分析结果:\n", "\n", "最大的10个值:\n", "1. 行 285233: 3330.0\n", "2. 行 285234: 3322.0\n", "3. 行 285235: 3317.98\n", "4. 行 285236: 3222.0\n", "5. 行 285232: 3133.0\n", "6. 行 285229: 3130.0\n", "7. 行 285231: 3060.0\n", "8. 行 285227: 3021.0\n", "9. 行 285226: 2980.01\n", "10. 行 285237: 2955.0\n", "\n", "最小的10个值:\n", "1. 行 275631: 25.42\n", "2. 行 275639: 25.51\n", "3. 行 275633: 25.6\n", "4. 行 275636: 25.65\n", "5. 行 275635: 25.8\n", "6. 行 275638: 25.8\n", "7. 行 275632: 26.1\n", "8. 行 275637: 26.2\n", "9. 行 275634: 26.3\n", "10. 行 275630: 26.8\n"]}], "source": ["import pandas as pd\n", "\n", "def find_extreme_values(csv_file, column_name, num_extremes=10):\n", "    \"\"\"\n", "    读取CSV文件并找出指定列中最大和最小的10个数字\n", "    \n", "    参数:\n", "        csv_file (str): CSV文件路径\n", "        column_name (str): 要分析的列名\n", "        num_extremes (int): 要查找的最大/最小值数量(默认为10)\n", "        \n", "    返回:\n", "        dict: 包含最大和最小值的字典\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(csv_file)\n", "        \n", "        # 检查列是否存在\n", "        if column_name not in df.columns:\n", "            raise ValueError(f\"列 '{column_name}' 不存在于CSV文件中\")\n", "            \n", "        # 转换为数值类型(自动处理非数值数据)\n", "        series = pd.to_numeric(df[column_name], errors='coerce')\n", "        \n", "        # 删除NaN值\n", "        clean_series = series.dropna()\n", "        \n", "        if len(clean_series) == 0:\n", "            raise ValueError(\"该列没有有效的数值数据\")\n", "            \n", "        # 获取最大的10个值\n", "        top_values = clean_series.nlargest(num_extremes)\n", "        \n", "        # 获取最小的10个值\n", "        bottom_values = clean_series.nsmallest(num_extremes)\n", "        \n", "        return {\n", "            'top_values': top_values.to_dict(),\n", "            'bottom_values': bottom_values.to_dict(),\n", "            'column': column_name,\n", "            'file': csv_file\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "        return None\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    # 替换为你的CSV文件路径和列名\n", "    file_path = \"cb_factors2.csv\"  \n", "    column_to_analyze = \"open\"  \n", "    \n", "    results = find_extreme_values(file_path, column_to_analyze)\n", "    \n", "    if results:\n", "        print(f\"\\n文件 '{results['file']}' 中列 '{results['column']}' 的分析结果:\")\n", "        \n", "        print(\"\\n最大的10个值:\")\n", "        for rank, (index, value) in enumerate(results['top_values'].items(), 1):\n", "            print(f\"{rank}. 行 {index}: {value}\")\n", "            \n", "        print(\"\\n最小的10个值:\")\n", "        for rank, (index, value) in enumerate(results['bottom_values'].items(), 1):\n", "            print(f\"{rank}. 行 {index}: {value}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "0a36a934", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始数据行数: 490797\n", "过滤后数据行数: 490796\n", "删除了 1 行\n", "结果已保存到: cb_factors3.csv\n"]}], "source": ["import pandas as pd\n", "\n", "def filter_csv_by_column_value(input_file, output_file, column_name, threshold):\n", "    \"\"\"\n", "    删除指定列中数值小于阈值的所有行\n", "    \n", "    参数:\n", "        input_file (str): 输入CSV文件路径\n", "        output_file (str): 输出CSV文件路径\n", "        column_name (str): 要过滤的列名\n", "        threshold (float): 阈值，小于此值的行将被删除\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(input_file)\n", "        \n", "        print(f\"原始数据行数: {len(df)}\")\n", "        \n", "        # 检查列是否存在\n", "        if column_name not in df.columns:\n", "            raise ValueError(f\"列 '{column_name}' 不存在于CSV文件中\")\n", "            \n", "        # 将指定列转换为数值类型(非数值转为NaN)\n", "        df[column_name] = pd.to_numeric(df[column_name], errors='coerce')\n", "        \n", "        # 删除小于阈值的行\n", "        filtered_df = df[df[column_name] >= threshold]\n", "        \n", "        # 删除转换过程中产生的NaN行(可选)\n", "        filtered_df = filtered_df.dropna(subset=[column_name])\n", "        \n", "        print(f\"过滤后数据行数: {len(filtered_df)}\")\n", "        print(f\"删除了 {len(df) - len(filtered_df)} 行\")\n", "        \n", "        # 保存到新文件\n", "        filtered_df.to_csv(output_file, index=False)\n", "        print(f\"结果已保存到: {output_file}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    # 替换为你的文件路径和参数\n", "    input_csv = \"cb_factors2.csv\"      # 输入文件\n", "    output_csv = \"cb_factors3.csv\"    # 输出文件\n", "    column = \"open\"            # 要过滤的列名\n", "    min_value = 1             # 阈值\n", "    \n", "    filter_csv_by_column_value(input_csv, output_csv, column, min_value)"]}, {"cell_type": "code", "execution_count": 2, "id": "72d00386", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成，结果已保存到 cb_factors1.csv\n", "原始行数: 545285，处理后行数: 495223\n"]}], "source": ["import pandas as pd\n", "\n", "def remove_rows_with_empty_columns(input_file, output_file, columns_to_check):\n", "    \"\"\"\n", "    删除指定列中为空值的整行\n", "    \n", "    参数:\n", "        input_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "        columns_to_check: 要检查的列名列表或单个列名\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 如果传入的是单个列名，转换为列表\n", "    if isinstance(columns_to_check, str):\n", "        columns_to_check = [columns_to_check]\n", "    \n", "    # 删除指定列中任何一列为空值的行\n", "    df_cleaned = df.dropna(subset=columns_to_check)\n", "    \n", "    # 保存处理后的数据\n", "    df_cleaned.to_csv(output_file, index=False)\n", "    print(f\"处理完成，结果已保存到 {output_file}\")\n", "    print(f\"原始行数: {len(df)}，处理后行数: {len(df_cleaned)}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors.csv\"      # 输入文件路径\n", "    output_csv = \"cb_factors1.csv\"    # 输出文件路径\n", "    columns = \"cb_momentum_60d\"  # 要检查的列名列表\n", "    \n", "    # 也可以只检查单个列\n", "    # columns = \"column1\"\n", "    # 多个列\n", "    #[\"column1\", \"column2\"] \n", "    remove_rows_with_empty_columns(input_csv, output_csv, columns)"]}, {"cell_type": "code", "execution_count": 9, "id": "af0f8f32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成，结果已保存到 cb_factors4.csv\n", "列 'cb_implied_volatility' 中共有 179399 个空值被替换为0\n"]}], "source": ["import pandas as pd\n", "\n", "def fill_empty_with_zero(input_file, output_file, column_name):\n", "    \"\"\"\n", "    将CSV文件中指定列的缺失值补0\n", "    \n", "    参数:\n", "        input_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "        column_name: 要处理的列名\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 统计补0前的空值数量\n", "    null_count_before = df[column_name].isnull().sum()\n", "    \n", "    # 将指定列的空值替换为0\n", "    df[column_name] = df[column_name].fillna(0)\n", "    \n", "    # 保存处理后的数据\n", "    df.to_csv(output_file, index=False)\n", "    \n", "    # 输出处理结果\n", "    print(f\"处理完成，结果已保存到 {output_file}\")\n", "    print(f\"列 '{column_name}' 中共有 {null_count_before} 个空值被替换为0\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors3.csv\"      # 输入文件路径\n", "    output_csv = \"cb_factors4.csv\"    # 输出文件路径\n", "    column = \"cb_implied_volatility\"  # 要处理的列名\n", "    \n", "    fill_empty_with_zero(input_csv, output_csv, column)"]}, {"cell_type": "code", "execution_count": 2, "id": "a6b67cae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "数据分析报告\n", "==================================================\n", "\n", "1. 文件中没有缺失值\n", "\n", "2. 文件中没有inf值\n", "\n", "3. trade_date列中最新的日期是: 2025-05-15 00:00:00\n", "\n", "3. trade_date列中最新的日期是: 2006-06-01 00:00:00\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "def analyze_data(csv_file):\n", "    \"\"\"\n", "    检查CSV文件中所有列的缺失值和inf值，并报告trade_date列中最新的日期\n", "    \n", "    参数:\n", "        csv_file: 要检查的CSV文件路径\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(csv_file)\n", "    \n", "    # 1. 检查缺失值\n", "    missing_data = {}\n", "    for column in df.columns:\n", "        null_rows = df[df[column].isnull()].index.tolist()\n", "        if null_rows:\n", "            missing_data[column] = [row + 1 for row in null_rows]  # +1因为索引从0开始\n", "    \n", "    # 2. 检查inf值\n", "    inf_data = {}\n", "    for column in df.select_dtypes(include=[np.number]).columns:  # 只检查数值列\n", "        inf_rows = df[np.isinf(df[column])].index.tolist()\n", "        if inf_rows:\n", "            inf_data[column] = [row + 1 for row in inf_rows]\n", "    \n", "    # 3. 检查trade_date列中最新的日期\n", "    latest_date = None\n", "    if 'trade_date' in df.columns:\n", "        try:\n", "            # 尝试转换为日期格式\n", "            df['trade_date'] = pd.to_datetime(df['trade_date'])\n", "            latest_date = df['trade_date'].max()\n", "        except:\n", "            # 如果转换失败，直接取最大值\n", "            latest_date = df['trade_date'].max()\n", "    oldest_date = None\n", "    if 'trade_date' in df.columns:\n", "        try:\n", "            # 尝试转换为日期格式\n", "            df['trade_date'] = pd.to_datetime(df['trade_date'])\n", "            oldest_date = df['trade_date'].min()\n", "        except:\n", "            # 如果转换失败，直接取最大值\n", "            oldest_date = df['trade_date'].min()\n", "    # 输出结果\n", "    print(\"=\"*50)\n", "    print(\"数据分析报告\")\n", "    print(\"=\"*50)\n", "    \n", "    # 缺失值报告\n", "    if not missing_data:\n", "        print(\"\\n1. 文件中没有缺失值\")\n", "    else:\n", "        print(\"\\n1. 发现以下缺失值:\")\n", "        for column, rows in missing_data.items():\n", "            print(f\"\\n列名: {column}\")\n", "            print(f\"缺失行数: {len(rows)}\")\n", "            print(f\"具体行号: {rows}\")\n", "    \n", "    # inf值报告\n", "    if not inf_data:\n", "        print(\"\\n2. 文件中没有inf值\")\n", "    else:\n", "        print(\"\\n2. 发现以下inf值:\")\n", "        for column, rows in inf_data.items():\n", "            print(f\"\\n列名: {column}\")\n", "            print(f\"包含inf的行数: {len(rows)}\")\n", "            print(f\"具体行号: {rows}\")\n", "    \n", "    # 最新日期报告\n", "    if latest_date is not None:\n", "        print(f\"\\n3. trade_date列中最新的日期是: {latest_date}\")\n", "    else:\n", "        print(\"\\n3. 文件中没有trade_date列\")\n", "    if oldest_date is not None:\n", "        print(f\"\\n3. trade_date列中最新的日期是: {oldest_date}\")\n", "    else:\n", "        print(\"\\n3. 文件中没有trade_date列\")\n", "\n", "if __name__ == \"__main__\":\n", "    file_path = \"cb_factors.csv\"\n", "    analyze_data(file_path)"]}, {"cell_type": "code", "execution_count": 12, "id": "64af7b33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成，结果已保存到: cb_factors6.csv\n", "原始行数: 492374\n", "删除行数: 660\n", "保留行数: 491714\n", "删除的行占总行数的: 0.13%\n"]}], "source": ["import pandas as pd\n", "\n", "def remove_rows_with_any_missing(input_file, output_file):\n", "    \"\"\"\n", "    删除CSV文件中任何列包含缺失值的整行\n", "    \n", "    参数:\n", "        input_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 记录原始行数\n", "    original_rows = len(df)\n", "    \n", "    # 删除任何列包含缺失值的行\n", "    df_cleaned = df.dropna(how='any')\n", "    \n", "    # 保存处理后的数据\n", "    df_cleaned.to_csv(output_file, index=False)\n", "    \n", "    # 计算删除的行数\n", "    removed_rows = original_rows - len(df_cleaned)\n", "    \n", "    # 输出结果\n", "    print(f\"处理完成，结果已保存到: {output_file}\")\n", "    print(f\"原始行数: {original_rows}\")\n", "    print(f\"删除行数: {removed_rows}\")\n", "    print(f\"保留行数: {len(df_cleaned)}\")\n", "    print(f\"删除的行占总行数的: {removed_rows/original_rows:.2%}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors5.csv\"      # 输入文件路径\n", "    output_csv = \"cb_factors6.csv\"    # 输出文件路径\n", "    \n", "    remove_rows_with_any_missing(input_csv, output_csv)"]}, {"cell_type": "code", "execution_count": 1, "id": "9950d02f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["发现 733 行包含inf/-inf值\n", "\n", "处理完成，结果已保存到: cb_factors6.csv\n", "原始行数: 492447\n", "删除行数: 733\n", "保留行数: 491714\n", "删除比例: 0.15%\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "def find_and_remove_inf(csv_file, output_file):\n", "    \"\"\"\n", "    查找并删除包含inf/-inf值的行\n", "    \n", "    参数:\n", "        csv_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(csv_file)\n", "    \n", "    # 记录原始行数\n", "    original_rows = len(df)\n", "    \n", "    # 查找包含inf/-inf的行\n", "    inf_mask = df.isin([np.inf, -np.inf]).any(axis=1)\n", "    inf_rows = df[inf_mask].index.tolist()\n", "    \n", "    if not inf_rows:\n", "        print(\"文件中未发现包含inf/-inf值的行\")\n", "        df.to_csv(output_file, index=False)\n", "        return\n", "    \n", "    # 显示检测结果\n", "    print(f\"发现 {len(inf_rows)} 行包含inf/-inf值\")\n", "    \n", "    # 删除这些行\n", "    df_cleaned = df[~inf_mask]\n", "    \n", "    # 保存处理后的数据\n", "    df_cleaned.to_csv(output_file, index=False)\n", "    \n", "    # 输出统计信息\n", "    print(f\"\\n处理完成，结果已保存到: {output_file}\")\n", "    print(f\"原始行数: {original_rows}\")\n", "    print(f\"删除行数: {len(inf_rows)}\")\n", "    print(f\"保留行数: {len(df_cleaned)}\")\n", "    print(f\"删除比例: {len(inf_rows)/original_rows:.2%}\")\n", "\n", "if __name__ == \"__main__\":\n", "    input_file = \"cb_factors5.csv\"    # 输入文件路径\n", "    output_file = \"cb_factors6.csv\"  # 输出文件路径\n", "    \n", "    find_and_remove_inf(input_file, output_file)"]}, {"cell_type": "code", "execution_count": 2, "id": "cc7e5dcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成，结果已保存到: cb_factors2.csv\n", "原始行数: 545285\n", "删除行数: 54488 (因其他列存在inf/空值)\n", "保留行数: 490797\n", "删除比例: 9.99%\n", "注意：列 'trade_date' 中的inf/空值会被保留\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "def clean_csv_except_column(input_file, output_file, protected_column):\n", "    \"\"\"\n", "    删除除指定列外的所有inf/-inf/空值所在行\n", "    \n", "    参数:\n", "        input_file: 输入CSV文件路径\n", "        output_file: 输出CSV文件路径\n", "        protected_column: 要保护的列名（该列的inf/空值不会被删除）\n", "    \"\"\"\n", "    # 读取CSV文件\n", "    df = pd.read_csv(input_file)\n", "    \n", "    # 检查保护列是否存在\n", "    if protected_column not in df.columns:\n", "        print(f\"错误：文件中不存在列 '{protected_column}'\")\n", "        return\n", "    \n", "    # 记录原始行数\n", "    original_rows = len(df)\n", "    \n", "    # 1. 找出除保护列外的其他列\n", "    other_columns = [col for col in df.columns if col != protected_column]\n", "    \n", "    # 2. 检查这些列中的inf/-inf/空值\n", "    # 创建掩码：标记出其他列中有问题的行\n", "    mask = (\n", "        df[other_columns].isin([np.inf, -np.inf, np.nan, '', ' '])\n", "        .any(axis=1)\n", "    )\n", "    \n", "    # 3. 删除这些行\n", "    df_cleaned = df[~mask]\n", "    \n", "    # 保存处理后的数据\n", "    df_cleaned.to_csv(output_file, index=False)\n", "    \n", "    # 输出统计信息\n", "    removed_rows = original_rows - len(df_cleaned)\n", "    print(f\"处理完成，结果已保存到: {output_file}\")\n", "    print(f\"原始行数: {original_rows}\")\n", "    print(f\"删除行数: {removed_rows} (因其他列存在inf/空值)\")\n", "    print(f\"保留行数: {len(df_cleaned)}\")\n", "    print(f\"删除比例: {removed_rows/original_rows:.2%}\")\n", "    print(f\"注意：列 '{protected_column}' 中的inf/空值会被保留\")\n", "\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors.csv\"       # 输入文件路径\n", "    output_csv = \"cb_factors2.csv\"    # 输出文件路径\n", "    protected_col = \"trade_date\"          # 要保护的列名（该列的inf/空值不删除）\n", "    \n", "    clean_csv_except_column(input_csv, output_csv, protected_col)"]}, {"cell_type": "code", "execution_count": 1, "id": "a54b2fcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV文件中的列名:\n", "[\n", "    'ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount', 'bond_value',\n", "    'bond_over_rate', 'cb_value', 'cb_over_rate', 'open_high_return_5d', 'cb_momentum_5d', 'cb_momentum_10d', 'cb_momentum_20d', 'cb_momentum_score', 'cb_momentum_consistency', 'cb_acceleration',\n", "    'cb_momentum_divergence', 'cb_low_vol_momentum', 'cb_atr', 'cb_natr', 'cb_volatility_decay', 'cb_volatility_regime', 'cb_garch_prediction', 'cb_range_expansion', 'cb_ma_5d', 'cb_ma_10d',\n", "    'cb_ma_20d', 'cb_bias_10d', 'cb_bias_20d', 'cb_trend_power', 'cb_early_trend_recognition', 'cb_rsi', 'cb_rsi_divergence', 'cb_volume_weighted_rsi', 'cb_kdj_k', 'cb_kdj_d',\n", "    'cb_kdj_j', 'cb_bb_position', 'cb_bb_width', 'cb_price_efficiency', 'cb_macd_cross_signal', 'cb_macd_zero_cross', 'cb_macd_hist', 'cb_adx', 'cb_plus_di', 'cb_minus_di',\n", "    'cb_di_diff', 'cb_roc_10d', 'cb_obv', 'cb_obv_trend', 'cb_mfi', 'cb_mfi_divergence', 'cb_smart_money', 'cb_volume_profile', 'cb_vp_momentum_penalty', 'cb_close_position_5d',\n", "    'cb_upper_shadow_5d', 'cb_lower_shadow_5d', 'cb_body_ratio_5d', 'cb_candle_color_ratio_5d', 'cb_close_position_10d', 'cb_upper_shadow_10d', 'cb_lower_shadow_10d', 'cb_body_ratio_10d', 'cb_candle_color_ratio_10d', 'cb_vwap',\n", "    'cb_avg_cost', 'cb_high_cost_ratio', 'cb_profit_ratio', 'cb_concentration', 'cb_fractal_indicator', 'cb_swing_strength', 'cb_high_low_position', 'cb_top_reversal_risk', 'cb_bottom_reversal_chance', 'cb_mean_reversion',\n", "    'cb_hidden_divergence', 'cb_failure_swing', 'cb_bond_equity_ratio', 'cb_arbitrage_window', 'cb_extreme_factor', 'stk_momentum_5d', 'stk_momentum_10d', 'stk_momentum_20d', 'return_diff_5d', 'cb_anti_chasing',\n", "    'cb_key_level_breakthrough', 'cb_pressure_release', 'cb_accumulation_signal', 'cb_pullback_opportunity', 'cb_continuation_pattern', 'cb_volume_price_confirmation', 'cb_momentum_quality', 'cb_trend_confirmation_index', 'cb_volatility_adjusted_momentum', 'cb_chande_momentum_oscillator',\n", "    'cb_advanced_breakout_score', 'cb_trend_stability_index', 'cb_pattern_recognition_score', 'cb_support_resistance_zones', 'cb_intraday_strength', 'cb_trend_regression_factor', 'cb_gap_analysis_factor', 'cb_opening_range_breakout', 'cb_momentum_divergence_factor', 'cb_volume_spike_reversal',\n", "    'cb_volume_price_trend', 'cb_anti_fomo_index', 'cb_exhaustion_indicator', 'cb_overbought_oscillator', 'cb_rush_index', 'cb_price_thrust_control', 'cb_retracement_opportunity', 'cb_consolidation_breakout', 'cb_overextension_risk', 'cb_sustainable_momentum',\n", "    'cb_buying_climax', 'cb_entry_timing_score', 'cb_profit_taking_signal', 'cb_stealth_accumulation', 'cb_overreaction_reversal', 'cb_fear_greed_indicator', 'cb_rally_sustainability', 'cb_balanced_entry_zone', 'cb_chasing_avoidance_score', 'cb_fakeout_detection',\n", "    'cb_market_resilience', 'cb_drawdown_control', 'cb_volatility_regime_control', 'cb_stop_loss_trigger', 'cb_risk_reward_balance', 'cb_trend_consistency_risk', 'cb_risk_concentration_index', 'cb_tail_risk_factor', 'cb_downside_protection', 'cb_max_adverse_excursion',\n", "    'cb_moving_stop_loss', 'cb_position_sizing_factor', 'cb_risk_oscillator', 'cb_optimal_exit_timing', 'cb_volatility_based_stop', 'cb_price_exhaustion', 'cb_trend_reversal_risk', 'cb_price_momentum_divergence', 'cb_profit_protection_ratio', 'cb_parabolic_warning',\n", "    'cb_momentum_reversal_probability', 'cb_risk_adjusted_momentum', 'cb_smart_trend_allocation', 'cb_cross_asset_momentum', 'cb_relative_value_factor', 'cb_optimal_conversion_timing', 'cb_regime_adaptive_factor', 'cb_tactical_timing_factor', 'cb_dynamic_hedging_factor', 'cb_triple_momentum_factor',\n", "    'cb_optimal_entry_exit_factor', 'cb_conversion_arbitrage_factor', 'cb_composite_signal_quality', 'cb_reaction_speed_factor', 'cb_probability_weighted_return', 'cb_normalized_composite_momentum', 'cb_extreme_regime_detector', 'cb_breakout_confirmation_index', 'cb_trend_efficiency_ratio', 'cb_price_velocity_factor',\n", "    'cb_stochastic_momentum_index', 'cb_washout_capitulation_detector', 'cb_contract_expansion_factor', 'cb_volatility_squeeze_release'\n", "]\n", "\n", "一共有 174 个列名\n"]}], "source": ["import pandas as pd\n", "\n", "def show_column_names(file_path):\n", "    \"\"\"\n", "    显示CSV文件的所有列名，按照Python数组格式打印，每行10个\n", "    \n", "    参数:\n", "        file_path (str): CSV文件路径\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(file_path)\n", "        columns = df.columns.tolist()\n", "        \n", "        # 打印Python数组格式的列名\n", "        print(\"CSV文件中的列名:\")\n", "        print(\"[\")\n", "        \n", "        # 每行打印10个元素\n", "        for i in range(0, len(columns), 10):\n", "            row = columns[i:i+10]\n", "            # 处理每行的最后一个元素是否有逗号\n", "            row_str = \", \".join(f\"'{col}'\" for col in row)\n", "            if i + 10 < len(columns):  # 不是最后一行就加逗号\n", "                row_str += \",\"\n", "            print(f\"    {row_str}\")\n", "            \n", "        print(\"]\")\n", "        \n", "        # 打印列名总数\n", "        print(f\"\\n一共有 {len(columns)} 个列名\")\n", "        \n", "        return columns\n", "    except FileNotFoundError:\n", "        print(f\"错误: 文件 '{file_path}' 未找到\")\n", "        return []\n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "        return []\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    file_path = 'cb_factors.csv'\n", "    show_column_names(file_path)"]}, {"cell_type": "code", "execution_count": null, "id": "707ae15b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "def remove_columns(file_path, columns_to_remove, output_file=None):\n", "    \"\"\"\n", "    从CSV文件中删除指定的列\n", "    \n", "    参数:\n", "        file_path (str): 输入CSV文件路径\n", "        columns_to_remove (list): 要删除的列名列表\n", "        output_file (str): 输出文件路径(可选)，如果为None则覆盖原文件\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(file_path)\n", "        \n", "        # 检查要删除的列是否存在\n", "        existing_columns = set(df.columns)\n", "        columns_to_remove = [col for col in columns_to_remove if col in existing_columns]\n", "        \n", "        if not columns_to_remove:\n", "            print(\"没有找到要删除的列\")\n", "            return\n", "            \n", "        # 删除指定列\n", "        df = df.drop(columns=columns_to_remove)\n", "        \n", "        # 保存结果\n", "        output_path = output_file if output_file else file_path\n", "        df.to_csv(output_path, index=False)\n", "        \n", "        print(f\"已成功删除列: {', '.join(columns_to_remove)}\")\n", "        print(f\"结果已保存到: {output_path}\")\n", "    except FileNotFoundError:\n", "        print(f\"错误: 文件 '{file_path}' 未找到\")\n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    file_path = 'cb_factors_train.csv'\n", "    \n", "    to_remove = input(\"请输入要删除的列名(多个列用逗号分隔): \").strip().split(',')\n", "    to_remove = [col.strip() for col in to_remove if col.strip()]\n", "        \n", "    if to_remove:\n", "        output_file = input(\"请输入输出文件路径(留空则覆盖原文件): \").strip() or None\n", "        remove_columns(file_path, to_remove, output_file)"]}, {"cell_type": "code", "execution_count": 6, "id": "5897d2d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成。已删除两年前的数据，结果保存到 cb_factors_2.csv\n", "原始数据行数: 310131\n", "处理后行数: 115904\n"]}], "source": ["import pandas as pd\n", "from datetime import datetime, timedelta\n", "\n", "def remove_old_data(input_file, output_file, date_column):\n", "    \"\"\"\n", "    读取CSV文件，删除两年前的数据，并保存到新文件\n", "    \n", "    参数:\n", "        input_file (str): 输入CSV文件路径\n", "        output_file (str): 输出CSV文件路径\n", "        date_column (str): 包含日期的列名\n", "    \"\"\"\n", "    try:\n", "        # 读取CSV文件\n", "        df = pd.read_csv(input_file)\n", "        \n", "        # 确保日期列存在\n", "        if date_column not in df.columns:\n", "            raise ValueError(f\"列 '{date_column}' 不存在于CSV文件中\")\n", "            \n", "        # 将日期列转换为datetime类型\n", "        df[date_column] = pd.to_datetime(df[date_column])\n", "        \n", "        # 计算两年前的日期\n", "        two_years_ago = datetime.now() - <PERSON><PERSON><PERSON>(days=400)  # 大约2年\n", "        \n", "        # 筛选出两年内的数据\n", "        filtered_df = df[df[date_column] >= two_years_ago]\n", "        \n", "        # 保存到新文件\n", "        filtered_df.to_csv(output_file, index=False)\n", "        \n", "        print(f\"处理完成。已删除两年前的数据，结果保存到 {output_file}\")\n", "        print(f\"原始数据行数: {len(df)}\")\n", "        print(f\"处理后行数: {len(filtered_df)}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"发生错误: {str(e)}\")\n", "\n", "# 使用示例\n", "if __name__ == \"__main__\":\n", "    input_csv = \"cb_factors.csv\"      # 输入CSV文件路径\n", "    output_csv = \"cb_factors_2.csv\"    # 输出CSV文件路径\n", "    date_col = \"trade_date\"            # 包含日期的列名\n", "    \n", "    remove_old_data(input_csv, output_csv, date_col)"]}], "metadata": {"kernelspec": {"display_name": "v8new", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.10"}}, "nbformat": 4, "nbformat_minor": 5}