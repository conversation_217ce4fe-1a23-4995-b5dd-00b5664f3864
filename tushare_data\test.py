import pandas as pd

def sort_and_export_csv(input_file, output_file, start_date, end_date, top_n):
    """
    读取CSV文件，按指定日期范围和cb_fractal_indicator排序，输出前N条记录
    
    参数:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
        start_date: 开始日期(格式: 'YYYY-MM-DD')
        end_date: 结束日期(格式: 'YYYY-MM-DD')
        top_n: 要输出的前N条记录
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        # 确保trade_date是日期类型
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 筛选指定日期范围
        mask = (df['trade_date'] >= start_date) & (df['trade_date'] <= end_date)
        filtered_df = df.loc[mask]
        
        # 按cb_fractal_indicator降序排序
        sorted_df = filtered_df.sort_values(by='cb_early_trend_recognition', ascending=False)
        
        # 取前N条记录
        top_df = sorted_df.head(top_n)
        
        # 保存到新的CSV文件
        top_df.to_csv(output_file, index=False)
        
        print(f"成功导出排序后的前{top_n}条记录到 {output_file}")
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

# 示例使用
if __name__ == "__main__":
    input_csv = "cb_factors_train.csv"  # 替换为你的输入CSV文件路径
    output_csv = "sorted_output.csv"  # 输出文件路径
    
    # 用户指定的参数
    start_date = '2023-01-01'  # 替换为你想要的开始日期
    end_date = '2023-12-31'    # 替换为你想要的结束日期
    top_n = 20                 # 替换为你想要的前N条记录
    
    sort_and_export_csv(input_csv, output_csv, start_date, end_date, top_n)