import os
import torch

# 数据配置
DATA_PATH = "tushare_data/cb_factors_train.csv"  # CSV文件路径
FACTORS = [
    'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount', 'bond_value',
    'bond_over_rate', 'cb_value', 'cb_over_rate', 'cb_momentum_5d', 'cb_momentum_10d', 'cb_momentum_20d', 'cb_momentum_score', 'cb_momentum_consistency', 'cb_acceleration',
    'cb_momentum_divergence', 'cb_low_vol_momentum', 'cb_atr', 'cb_natr', 'cb_volatility_decay', 'cb_volatility_regime', 'cb_range_expansion', 'cb_ma_5d', 'cb_ma_10d', 'cb_ma_20d',
    'cb_bias_10d', 'cb_bias_20d', 'cb_trend_power', 'cb_early_trend_recognition', 'cb_rsi', 'cb_rsi_divergence', 'cb_volume_weighted_rsi', 'cb_kdj_k', 'cb_kdj_d', 'cb_kdj_j',
    'cb_bb_position', 'cb_bb_width', 'cb_price_efficiency', 'cb_macd_cross_signal', 'cb_macd_zero_cross', 'cb_macd_hist', 'cb_adx', 'cb_plus_di', 'cb_minus_di', 'cb_di_diff',
    'cb_roc_10d', 'cb_obv', 'cb_obv_trend', 'cb_mfi', 'cb_mfi_divergence', 'cb_smart_money', 'cb_volume_profile', 'cb_vp_momentum_penalty', 'cb_close_position_5d', 'cb_upper_shadow_5d',
    'cb_lower_shadow_5d', 'cb_body_ratio_5d', 'cb_candle_color_ratio_5d', 'cb_close_position_10d', 'cb_upper_shadow_10d', 'cb_lower_shadow_10d', 'cb_body_ratio_10d', 'cb_candle_color_ratio_10d', 'cb_vwap', 'cb_avg_cost',
    'cb_high_cost_ratio', 'cb_profit_ratio', 'cb_concentration', 'cb_fractal_indicator', 'cb_swing_strength', 'cb_high_low_position', 'cb_top_reversal_risk', 'cb_bottom_reversal_chance', 'cb_mean_reversion', 'cb_hidden_divergence',
    'cb_failure_swing', 'cb_bond_equity_ratio', 'cb_arbitrage_window', 'cb_extreme_factor', 'stk_momentum_5d', 'stk_momentum_10d', 'stk_momentum_20d', 'return_diff_5d', 'cb_anti_chasing', 'cb_key_level_breakthrough',
    'cb_pressure_release', 'cb_accumulation_signal', 'cb_pullback_opportunity', 'cb_continuation_pattern', 'cb_volume_price_confirmation', 'cb_momentum_quality', 'cb_trend_confirmation_index', 'cb_volatility_adjusted_momentum', 'cb_chande_momentum_oscillator', 'cb_advanced_breakout_score',
    'cb_trend_stability_index', 'cb_pattern_recognition_score', 'cb_support_resistance_zones', 'cb_intraday_strength', 'cb_trend_regression_factor', 'cb_gap_analysis_factor', 'cb_opening_range_breakout', 'cb_momentum_divergence_factor', 'cb_volume_spike_reversal', 'cb_volume_price_trend',
    'cb_anti_fomo_index', 'cb_exhaustion_indicator', 'cb_overbought_oscillator', 'cb_rush_index', 'cb_price_thrust_control', 'cb_retracement_opportunity', 'cb_consolidation_breakout', 'cb_overextension_risk', 'cb_sustainable_momentum', 'cb_buying_climax',
    'cb_entry_timing_score', 'cb_profit_taking_signal', 'cb_stealth_accumulation', 'cb_overreaction_reversal', 'cb_fear_greed_indicator', 'cb_rally_sustainability', 'cb_balanced_entry_zone', 'cb_chasing_avoidance_score', 'cb_fakeout_detection', 'cb_market_resilience',
    'cb_drawdown_control', 'cb_volatility_regime_control', 'cb_stop_loss_trigger', 'cb_risk_reward_balance', 'cb_trend_consistency_risk', 'cb_risk_concentration_index', 'cb_tail_risk_factor', 'cb_downside_protection', 'cb_max_adverse_excursion', 'cb_moving_stop_loss',
    'cb_position_sizing_factor', 'cb_risk_oscillator', 'cb_optimal_exit_timing', 'cb_volatility_based_stop', 'cb_price_exhaustion', 'cb_trend_reversal_risk', 'cb_price_momentum_divergence', 'cb_profit_protection_ratio', 'cb_parabolic_warning', 'cb_momentum_reversal_probability',
    'cb_risk_adjusted_momentum', 'cb_smart_trend_allocation', 'cb_cross_asset_momentum', 'cb_relative_value_factor', 'cb_optimal_conversion_timing', 'cb_regime_adaptive_factor', 'cb_tactical_timing_factor', 'cb_dynamic_hedging_factor', 'cb_triple_momentum_factor', 'cb_optimal_entry_exit_factor',
    'cb_conversion_arbitrage_factor', 'cb_composite_signal_quality', 'cb_reaction_speed_factor', 'cb_probability_weighted_return', 'cb_normalized_composite_momentum', 'cb_extreme_regime_detector', 'cb_breakout_confirmation_index', 'cb_trend_efficiency_ratio', 'cb_price_velocity_factor', 'cb_stochastic_momentum_index',
    'cb_contract_expansion_factor', 'cb_volatility_squeeze_release'
]

DEFAULT_FACTORS = [
    'cb_fractal_indicator', 'cb_natr', 'cb_kdj_d', 'bond_value',
    'cb_early_trend_recognition', 'cb_volatility_regime', 'cb_kdj_j', 'cb_pullback_opportunity',
    'bond_over_rate', 'cb_obv', 'cb_profit_taking_signal', 'cb_volume_profile',
    'cb_minus_di', 'cb_rsi_divergence', 'cb_failure_swing', 'cb_adx',
    'cb_di_diff', 'cb_reaction_speed_factor', 'cb_plus_di', 'cb_obv_trend',
    'cb_probability_weighted_return', 'cb_range_expansion', 'cb_volume_price_trend', 'cb_macd_hist',
    'cb_upper_shadow_5d', 'cb_normalized_composite_momentum', 'cb_atr', 'cb_smart_money',
    'cb_extreme_factor', 'stk_momentum_20d', 'cb_stealth_accumulation', 'cb_risk_oscillator',
    'cb_chande_momentum_oscillator', 'cb_trend_stability_index', 'cb_arbitrage_window', 'cb_price_momentum_divergence',
    'cb_over_rate', 'pct_chg', 'cb_exhaustion_indicator', 'cb_bond_equity_ratio',
    'cb_price_velocity_factor', 'cb_mfi_divergence', 'cb_lower_shadow_10d', 'cb_close_position_10d',
    'cb_momentum_reversal_probability', 'cb_trend_regression_factor', 'cb_bias_10d', 'cb_acceleration',
    'cb_profit_ratio', 'cb_moving_stop_loss', 'cb_profit_protection_ratio', 'cb_parabolic_warning',
    'cb_volatility_decay', 'cb_volatility_based_stop', 'cb_trend_consistency_risk', 'cb_kdj_k',
    'cb_stochastic_momentum_index', 'amount', 'cb_optimal_exit_timing', 'stk_momentum_10d',
    'cb_dynamic_hedging_factor', 'cb_body_ratio_10d', 'cb_volume_weighted_rsi', 'vol',
    'cb_momentum_5d', 'cb_value', 'stk_momentum_5d', 'cb_mfi',
    'cb_lower_shadow_5d', 'cb_retracement_opportunity', 'cb_upper_shadow_10d', 'cb_close_position_5d',
    'cb_optimal_conversion_timing', 'cb_swing_strength', 'return_diff_5d', 'cb_entry_timing_score',
    'cb_drawdown_control', 'cb_buying_climax', 'cb_rsi', 'cb_max_adverse_excursion',
    'cb_bottom_reversal_chance', 'low', 'cb_price_exhaustion', 'cb_trend_reversal_risk',
    'cb_advanced_breakout_score', 'cb_momentum_divergence', 'cb_smart_trend_allocation', 'cb_position_sizing_factor',
    'cb_momentum_score', 'cb_body_ratio_5d', 'cb_ma_10d', 'cb_price_efficiency',
    'cb_low_vol_momentum', 'cb_high_low_position', 'cb_relative_value_factor', 'cb_pattern_recognition_score',
    'cb_pressure_release', 'close', 'cb_contract_expansion_factor', 'cb_trend_power',
]



# 目标变量
TARGET = "open_high_return_5d"

# 模型配置
LOOKBACK_WINDOW = 20  # 历史数据窗口大小
BATCH_SIZE = 64      # 批处理大小
D_MODEL = 64          # 模型维度
N_LAYER = 6           # Transformer层数
NUM_HEADS = 8         # 多头注意力头数
GNN_K = 4             # 图神经网络的多项式阶数
NODE_EMBEDDING_DIM = 32  # 节点嵌入维度
CNN_BLOCKS = 2               # Inception块的数量
CNN_KERNEL_SIZES = [3, 5, 7] # 多尺度卷积核大小列表
CNN_BOTTLENECK_SCALE = 0.5   # 瓶颈层通道缩放比例 (相对于d_model)
DROPOUT = 0.1


# 训练配置
EPOCHS = 300         # 训练轮数
LEARNING_RATE = 0.001  # 学习率
WEIGHT_DECAY = 1e-4   # 权重衰减
SAVE_INTERVAL = 10    # 权重保存间隔
EARLY_STOP_PATIENCE = 30  # 早停耐心

# 路径配置
OUTPUT_DIR = "output"  # 输出目录
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设备配置
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
